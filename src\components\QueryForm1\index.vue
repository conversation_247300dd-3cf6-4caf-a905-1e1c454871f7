<template>
  <el-form
    ref="form"
    class="query-form"
    :model="form"
    :disabled="disabled"
    label-position="right"
    :label-width="labelWidth"
  >
    <el-row>
      <el-col v-if="conditions.displayAdd" :span="24" class="add-btn">
        <HandelBtn :btn="{ type: 'handleAdd', text: '重置', width: '65px' }" @handleAdd="onReset()"></HandelBtn>
        <span class="body_tip">
          <i v-if="tipIcon" :class="tipIcon" :style="{ color: tipIconColor }"></i>
          {{ tip }}
        </span>
      </el-col>
      <el-col v-for="value in conditions.items" :key="value.prop" :span="value.span">
        <div v-if="value.detailsTitle" class="query-form-details-title">{{ value.detailsTitle }}</div>
        <el-form-item
          v-if="!value.noshow"
          :label="`${value.label}:`"
          :style="{ visibility: value.unVisible ? 'hidden' : 'visible' }"
          :prop="value.prop"
          :class="value.style ? value.style : 'default'"
          :rules="value.pattern
            ? {
              required: value.required ? value.required : false,
              message: value.message ? value.message : `请输入合法的${value.label}`,
              pattern: value.pattern
            }
            : {
              required: value.required ? (value.showLongTerm ? !value.longTerm : value.required) : false,
              message: `${value.label}不能为空`
            }
          "
        >
          <div v-if="value.type === 'el-radio-group'">
            <el-radio-group v-model="form[value.prop]">
              <el-radio
                v-for="sel in value.select"
                :key="sel.value"
                :label="sel.label"
                :value="sel.value"
                @change="changeCondition(sel.value, value.type, value.prop)"
              ></el-radio>
            </el-radio-group>
            <span v-if="value.tips" class="radio-tips">{{ value.tips }}</span>
          </div>
          <el-cascader
            v-else-if="value.type === 'el-cascader'"
            v-model="form[value.prop]"
            :options="value.options"
            @change="handleChangeCascader(value.value, value.type, value.prop)"
          />
          <el-autocomplete
            v-else-if="value.type === 'el-autocomplete'"
            :ref="value.prop"
            v-model="form[value.prop]"
            :popper-class="isNo ? 'no-autocomplete' : 'have-autocomplete'"
            hide-loading
            :fetch-suggestions="(queryString, callback) => {
              querySearchAsync(queryString, callback, value.prop)
            }
            "
            :placeholder="value.placeholder"
            @blur="blurRequest"
            @select="item => {
              handleSelect(item, value.prop)
            }
            "
          >
            <template slot-scope="{ item }">
              <div class="complete">
                <a href="javascript:;" :title="`${item.value}${item.desc}`">
                  <span class="name">{{ item.value }}</span><span class="desc">{{ item.desc }}</span></a>
              </div>
            </template>
          </el-autocomplete>
          <div v-else-if="value.type === 'el-upload'">
            <el-upload
              v-if="!value.status || value.status === 'save' || value.status === 'notApproved'"
              v-bind="value.uploadOptions"
              class="upload-form"
              :drag="value.drag"
              :headers="{ 'X-Requested-With': null }"
              :action="addressOss"
              :limit="value.limit || 1"
              :data="dataOss"
              :accept="value.accept"
              :file-list="value.prop && form[value.prop] ? form[value.prop] : []"
              :on-remove="(file, files) => {
                handleRemove(file, files, value.prop)
              }
              "
              :before-upload="getPolicy"
              :on-success="(response, file, fileList) => {
                uploadSuccess(response, file, fileList, value.prop)
              }
              "
              :on-error="uploadError"
            >
              <div v-if="value.uploadTip" slot="tip" class="upload-tip">{{ value.uploadTip }}</div>
              <div>
                <div v-if="!value.drag" style="padding-right: 100px;">
                  <el-button size="small" type="primary">点击上传</el-button>
                </div>
                <div v-else>
                  <i class="el-icon-upload"></i>
                  <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
                </div>
              </div>
            </el-upload>
            <div v-else class="file-list">
              <div
                v-for="(file, index) in form[value.prop]"
                :key="index"
                class="file-item"
                @click="openUrl(file.url, file.name)"
              >
                <span class="file-name">{{ file.name }}</span>
              </div>
            </div>
          </div>
          <component
            :is="getElement(value.type)"
            v-else
            :ref="value.prop"
            v-model.trim="form[value.prop]"
            :type="value.eltype ? value.eltype : null"
            :multiple="value.multiple"
            :rows="value.row"
            :placeholder="value.disabled ? '-' : value.placeholder"
            :range-separator="value.rangeSeparator"
            :start-placeholder="value.startPlaceholder"
            :end-placeholder="value.endPlaceholder"
            :disabled="value.disabled"
            :allow-create="value.allowCreate"
            :filterable="value.filterable"
            :clearable="value.clearable"
            :default-first-option="value.defaultFirstOption"
            :popper-append-to-body="!value.popperAppendToBody"
            :picker-options="setPickerOptions(value.prop)"
            value-format="timestamp"
            :default-time="value.eltype === 'daterange' ? ['00:00:00', '23:59:59'] : ''"
            :style="value.showLongTerm ? 'width: 80%' : 'width: 100%'"
            @change="changeCondition($event, value.type, value.prop)"
          >
            <component
              :is="getElement(value.seltype)"
              v-for="sel in value.select"
              :key="sel.value"
              :label="sel.label"
              :value="sel.value"
            ></component>
          </component>
          <el-checkbox
            v-if="value.showLongTerm"
            v-model="value.longTerm"
            label="长期"
            style="margin-left: 10px; width: 10%"
          ></el-checkbox>
          <el-tooltip v-if="value.tip" class="item" effect="light" :content="value.tip" placement="top">
            <i class="el-icon-warning icon"></i>
          </el-tooltip>
        </el-form-item>
      </el-col>
      <template v-if="conditions.rangDate">
        <el-col v-for="item in conditions.rangDate" :key="item.propStartAt" :span="item.span" class="daterange">
          <el-form-item :label="`${item.label}:`" :prop="item.propStartAt">
            <el-date-picker
              v-model="form[item.propStartAt]"
              type="date"
              value-format="yyyy-MM-dd"
              :clearable="false"
              :placeholder="item.startPlaceholder"
            >
            </el-date-picker>
          </el-form-item>
          <span class="symbol">-</span>
          <el-form-item class="rightdate" :prop="item.propEndAt">
            <el-date-picker
              v-model="form[item.propEndAt]"
              type="date"
              value-format="yyyy-MM-dd"
              :clearable="false"
              :placeholder="item.endPlaceholder"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
      </template>
      <el-col v-if="conditions.displayBtn" :span="conditions.displayBtnSpan ? conditions.displayBtnSpan : 8">
        <el-form-item class="btns">
          <el-button type="primary" size="mini" class="searchBtn" @click="onSubmit()">查询</el-button>
          <el-button size="mini" class="searchBtn" @click="onReset()">重置</el-button>
          <el-button v-if="exports" size="mini" type="text" @click="onExport()">导出</el-button>
          <el-button v-if="conditions.expend && !conditions.isAll" type="text" @click="onExpend(true)">展开 <i class="el-icon-arrow-down"></i></el-button>
          <el-button v-if="conditions.expend && conditions.isAll" type="text" @click="onExpend(false)">收起 <i class="el-icon-arrow-up"></i></el-button>
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>
<script>
import HandelBtn from '@/components/HandleBtn/index'

export default {
  components: { HandelBtn },
  props: {
    labelWidth: {
      type: String,
      default: '150px'
    },
    exports: {
      type: Boolean,
      default: false
    },
    tipIcon: {
      type: String,
      default: ''
    },
    tipIconColor: {
      type: String,
      default: ''
    },
    tip: {
      type: String,
      default: ''
    },
    imageType: {
      type: String,
      default: 'admin'
    },
    disabled: {
      type: Boolean,
      default: false
    },
    queryResults: {
      type: Array,
      default() {
        return []
      }
    },
    conditions: {
      type: Object,
      default() {
        return {
          field: {},
          items: []
        }
      }
    }
  },
  data() {
    return {
      dir: '',
      fileNames: '',
      fileField: '',
      filePath: '',
      fileName: '',
      dataOss: {},
      addressOss: '',
      form: {},
      pickerOptions: {},
      cb: null,
      isNo: false
    }
  },
  watch: {
    conditions: {
      handler(newCond, oldCond) {
        this.form = newCond.field || {}
      },
      immediate: true,
      deep: true
    },
    queryResults(newRes, oldRes) {
      if (newRes[0]) this.isNo = newRes[0].noData
      if (this.cb) {
        this.cb(newRes)
      }
    }
  },
  beforeMount() {
    this.form = this.conditions.field
  },
  methods: {
    onExport() {
      this.$emit('onExport', this.form)
    },
    onExpend(isAll) {
      this.$emit('onExpend', isAll)
    },
    getElement(element) {
      return element
    },
    setPickerOptions(key) {
      const dates = ['purchaseDate', 'storageTime']
      if (dates.indexOf(key) !== -1) {
        return {
          disabledDate(time) {
            return time.getTime() > Date.now()
          }
        }
      }
      if (key === 'outTime') {
        return {
          disabledDate(time) {
            return time.getTime() < Date.now() - 8.64e7
          }
        }
      }
    },
    blurRequest() { },
    changeCondition(e, type, name) {
      this.$emit('onChange', e, type, name)
    },
    handleChangeCascader(e, type, name) {
      this.$emit('onChangeCascader', e, type, name)
    },
    onSubmit() {
      let isValid = false
      if (this.form.statuses === '') this.form.statuses = null
      this.$refs.form.validate((valid, object) => {
        if (valid) {
          this.$emit('onSubmit', this.form)
          isValid = this.form
        } else {
          if (Object.keys(object) && Object.keys(object)[0]) {
            this.$refs[Object.keys(object)[0]][0].focus()
          }
          return false
        }
      })
      return isValid
    },
    // 查询表单不需要校验
    handleSearch() {
      return this.form
    },
    onReset() {
      Object.keys(this.form).forEach(key => {
        this.form[key] = null
      })
      this.$refs.form.clearValidate()
      this.$refs.form.resetFields()
      this.$emit('onReset', this.form)
    },
    querySearchAsync(queryString, cb, type) {
      this.$emit('querySearch', queryString, type)
      this.cb = cb
    },
    handleSelect(item, type) {
      this.$emit('selectResult', item, type)
    },
    getPolicy(file) {
      console.log(1111)
      return new Promise((resolve, reject) => {
        const type = file.name.substring(file.name.lastIndexOf('.'))
        const params = {
          url: 'common/file/getFileUploadPolicy',
          data: {
            imageType: this.imageType,
            fileSuffix: type
          }
        }
        this.execute(params).then(res => {
          if (res.success) {
            const fileName = res.data.fileName
            this.filePath = fileName
            this.fileName = fileName
            this.fileNames = file.name
            this.addressOss = this.$replaceDomain(res.data.host)
            this.dataOss = {
              key: fileName,
              policy: res.data.policy,
              AWSAccessKeyId: res.data.accessKeyId,
              Signature: res.data.signature
            }
            resolve()
          } else {
            this.$message.error('获取上传权限失败')
            reject()
          }
        })
      })
    },
    handleRemove(file, fileList, prop) {
      if (file.status === 'success') {
        this.form[prop] = fileList
      } else {
        this.$message.error('文件删除失败，请稍后重试')
      }
    },
    uploadSuccess(res, file, fileList, prop) {
      if (file.status === 'success') {
        const params = {
          url: 'common/file/getFileViewUrl',
          data: {
            fileName: this.fileName
          }
        }
        this.execute(params).then(res => {
          if (res.success) {
            this.form[prop].push({
              fileName: this.fileName,
              name: file.name,
              url: res.data
            })
          } else {
            this.$message.error(res.message || '获取文件地址失败')
          }
        }).catch(e => {
          this.$message.error('获取文件地址失败')
        })
      } else {
        this.$message.error('文件上传失败，请稍后重试')
      }
    },
    uploadError() {
      this.$message.error('文件上传失败，请稍后重试')
    },
    openUrl(url, name) {
      const params = {
        interfaceName: 'com.alibaba.ewtp.psp.service.api.OSSConfigServiceService',
        methodName: 'getFileUrl',
        dir: this.dir,
        name,
        url
      }
      this.execute(params).then(res => {
        if (res.success) {
          window.open(res.data, '_blank')
        } else {
          this.$message.error(res.message || '获取文件地址失败')
        }
      }).catch(e => {
        this.$message.error('获取文件地址失败')
      })
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .el-input__inner,
::v-deep .el-form-item__label,
::v-deep .el-textarea__inner,
::v-deep .el-range-input,
::v-deep .el-range-separator,
::v-deep .el-range__icon,
::v-deep .el-radio__label,
::v-deep .el-checkbox__label {
  font-size: 14px;
}

.body_tip {
  font-size: 10px;
  color: #2c83e9;
  vertical-align: bottom;
  display: inline-block;
  margin-left: 20px;
}

.body_tip i {
  position: static;
  float: none;
}

.radio-tips {
  font-size: 13px;
  color: #f32c43;
  margin-left: 20px;
}

.upload-tip {
  font-size: 12px;
  color: #f32c43;
}

.file-list {
  .file-item {
    color: #606266;

    .file-name {
      margin-right: 15px;
      font-size: 12px;
      cursor: pointer;

      &:hover {
        color: #288dfd;
      }
    }

    .el-icon-download {
      font-size: 16px;
      vertical-align: -4px;
      cursor: pointer;

      &:hover {
        color: #288dfd;
      }
    }
  }
}

.icon {
  position: absolute;
  top: 35%;
  margin-left: 5px;
  color: #e6a23c;
  float: right;
  cursor: pointer;
}

.btns {
  display: flex;
  justify-content: flex-end;

  ::v-deep .el-button--mini {
    padding: 7px 13px;
  }

  ::v-deep .el-form-item__content {
    margin-left: 0 !important;
  }
  .searchBtn{
    height: 32px;
  }
}

.add-btn {
  margin-bottom: 15px;
}

.maxlength {
  ::v-deep .el-form-item__content {
    width: calc(50% + 360px);
  }

  ::v-deep .el-input__inner,
  .el-select {
    width: 100%;
  }
}

.maxlength_1 {
  ::v-deep .el-input__inner {
    height: 32px;
    line-height: 32px;
  }

  // ::v-deep .el-form-item__content {
  //   width: calc(50% + 430px);
  // }
  ::v-deep .el-input__inner,
  .el-select {
    width: 100%;
  }
}

.declaration {
  ::v-deep .el-input__inner {
    height: 32px;
    line-height: 32px;
  }

  ::v-deep .el-input__inner,
  ::v-deep .el-date-editor,
  ::v-deep .el-select {
    width: 100%;
  }

  ::v-deep .el-form-item {
    margin-bottom: 20px;
  }
}

.default {
  display: flex;
  align-items: center;
  margin-bottom: 15px;

  ::v-deep .el-input__inner {
    height: 32px;
    line-height: 32px;
  }

  ::v-deep .el-input__inner,
  ::v-deep .el-date-editor,
  ::v-deep .el-select {
    width: 100%;
  }

  ::v-deep .el-range-separator,
  ::v-deep .el-range__icon,
  ::v-deep .el-range__close-icon,
  ::v-deep .el-radio__label,
  ::v-deep .el-checkbox__label {
    line-height: 20px;
  }

  ::v-deep .el-cascader {
    width: 100%;
  }

  ::v-deep .el-tag--small {
    height: 20px;
    line-height: 19px;
  }

  ::v-deep .el-form-item__label {
    line-height: 15px;
  }

  ::v-deep .el-form-item__content {
    margin-left: 0 !important;
    flex: 1;
  }
}

.application {
  display: flex;
  align-items: center;
  margin-bottom: 15px;

  ::v-deep .el-input__inner,
  ::v-deep .el-date-editor,
  ::v-deep .el-select {
    width: 225px;
  }

  ::v-deep .el-form-item__content {
    margin-left: 0 !important;
  }

  ::v-deep .el-input__inner {
    height:32px;
    line-height: 32px;
  }

  ::v-deep .el-form-item__label {
    line-height: 15px;
  }
}

.daterange {
  display: flex;
  align-items: center;

  .symbol {
    padding: 0 12px;
    margin-bottom: 15px;
    font-size: 16px;
    color: #909399;
  }

  ::v-deep .el-form-item {
    margin-bottom: 15px;
    width: 100%;
  }

  ::v-deep .el-input__inner {
    height: 32px;
    line-height: 32px;
  }

  ::v-deep .el-input__inner,
  ::v-deep .el-date-editor {
    width: 120px;
    padding-right: 0;
  }

  .rightdate {
    ::v-deep .el-form-item__content {
      margin-left: 0 !important;
    }
  }
}

.have-autocomplete {
  li {
    line-height: normal;
    padding: 7px;
    display: flex;
    align-items: center;

    .complete {
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .desc {
      font-size: 12px;
      color: #b4b4b4;
    }

    .highlighted .addr {
      color: #ddd;
    }
  }
}

.no-autocomplete {
  li {
    line-height: normal;
    padding: 7px;
    display: flex;
    align-items: center;

    .complete {
      cursor: default;
      text-align: center;
    }

    .desc {
      font-size: 12px;
      color: #b4b4b4;
    }

    .highlighted .addr {
      color: #ddd;
    }
  }
}

.query-form {
  padding: 24px 24px 0 24px;
}

.query-form-details-title {
  padding: 20px 0;
  font-weight: 600;
  font-size: 18px;
}

.search-condition-icon {
  font-size: 16px;
  height: 46px;
  line-height: 46px;
  border-bottom: solid 1px #3366FF;
  img {
    width: 13px;
    height: 16px;
  }
}
</style>
