<template>
  <div class="title-container">
    <div class="content">
      <div class="title-left">
        <img src="@/assets/images/title-icon.png" alt="" />
        <span>{{ title }}</span>
      </div>
      <div class="title-right">
        <slot name="company"></slot>
        <slot name="collapse"></slot>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'SmTitle',
  data() {
    return {}
  },
  props: {
    title: {
      type: String,
      default: '标题'
    }
  }
}
</script>

<style lang="scss" scoped>
.title-container {
  background-color: #fff;
  border-bottom: 2px solid #3366ff;
  .content {
    height: 46px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 24px;
    font-weight: 500;
    font-size: 16px;
    color: rgba(0, 0, 0, 0.85);
    img {
      width: 13px;
      height: 16px;
      margin-right: 8px;
    }
    .title-left {
        display: flex;
        align-items: center;
        justify-content: center;
    }
    .title-right {
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}
</style>
