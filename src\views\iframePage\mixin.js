import { getToken } from '@/utils/auth'

export default {
  methods: {
    onIframeLoad() {
      // iframe加载完成后再发送消息
      const iframe = document.querySelector('iframe')
      if (iframe) {
        const param = {
          sign: 'shumao',
          // parentRouter: this.$router,
          token: getToken()
        }
        console.log(`父组件传输的数据=`, param)

        iframe.contentWindow.postMessage(
          {
            type: 'parentData',
            data: param
          },
          '*'
        )
      }
    }
  },

  mounted() {
    window.currentRouter = this.$router // 把router注入iframe页面

    //点击新增申报时候，调用注入iframe页面的currentRouter，挂在window上。新增页面路由为/out-link/addIframePage
    // const routeName = '/firstimportsjy' // /secondgimportdeclnx
    
    // window.parent.currentRouter.push(`/out-link/addIframePage?routeName=${routeName}`)
  },

  beforeDestroy() {
    // 清理事件监听
    window.removeEventListener('message', this.onMessage)
  }
}
