{"name": "yqyc-ent", "version": "1.0.0", "description": "", "author": "", "scripts": {"serve": "vue-cli-service serve", "lint": "vue-cli-service lint", "build:prod": "vue-cli-service build", "build:test": "vue-cli-service build --mode test", "build:stage": "vue-cli-service build --mode staging", "preview": "node build/index.js --preview", "svgo": "svgo -f src/icons/svg --config=src/icons/svgo.yml", "test:unit": "jest --clearCache && vue-cli-service test:unit", "test:ci": "npm run lint && npm run test:unit", "commit": "git-cz"}, "dependencies": {"@tinymce/tinymce-vue": "^3.2.8", "ali-oss": "^6.15.2", "axios": "0.18.1", "bignumber.js": "^9.3.0", "clipboard": "2.0.4", "core-js": "3.6.5", "dayjs": "^1.8.36", "element-ui": "2.13.2", "file-saver": "2.0.1", "js-cookie": "2.2.0", "jsencrypt": "3.0.0-rc.1", "jszip": "3.2.1", "moment": "^2.27.0", "normalize.css": "7.0.0", "nprogress": "0.2.0", "path-to-regexp": "2.4.0", "quill-image-drop-module": "^1.0.3", "quill-image-resize-module": "^3.0.0", "screenfull": "4.2.0", "vue": "^2.6.10", "vue-count-to": "^1.0.13", "vue-i18n": "7.3.2", "vue-router": "3.0.2", "vuex": "3.1.0", "xlsx": "0.14.1"}, "devDependencies": {"@commitlint/cli": "^9.1.2", "@commitlint/config-conventional": "^9.1.2", "@jridgewell/gen-mapping": "0.3.5", "@vue/cli-plugin-babel": "^4.0.4", "@vue/cli-plugin-eslint": "^4.0.4", "@vue/cli-plugin-router": "^4.0.4", "@vue/cli-plugin-unit-jest": "^4.0.4", "@vue/cli-plugin-vuex": "^4.0.4", "@vue/cli-service": "^4.0.4", "@vue/eslint-config-standard": "^5.1.2", "@vue/test-utils": "1.0.0-beta.29", "autoprefixer": "9.5.1", "babel-eslint": "10.1.0", "babel-jest": "23.6.0", "babel-plugin-component": "^1.1.1", "babel-plugin-dynamic-import-node": "2.3.3", "chalk": "2.4.2", "chokidar": "2.1.5", "commitizen": "^4.2.1", "connect": "3.6.6", "cz-conventional-changelog": "^3.3.0", "decimal.js": "^10.3.1", "eslint": "^6.7.2", "eslint-plugin-import": "^2.20.1", "eslint-plugin-node": "^11.0.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.0", "eslint-plugin-vue": "^6.1.2", "html-webpack-plugin": "3.2.0", "lint-staged": "8.1.5", "mockjs": "1.0.1-beta3", "runjs": "4.3.2", "sass": "1.26.2", "sass-loader": "8.0.2", "script-ext-html-webpack-plugin": "2.1.3", "serve-static": "1.13.2", "svg-sprite-loader": "4.1.3", "svgo": "1.2.0", "vue-template-compiler": "^2.6.10"}, "browserslist": ["> 1%", "last 2 versions"], "engines": {"node": ">=8.9", "npm": ">= 3.0.0"}, "config": {"commitizen": {"path": "node_modules/cz-conventional-changelog"}}, "gitHooks": {"pre-commit": "lint-staged", "commit-msg": "commitlint -E GIT_PARAMS"}, "lint-staged": {"*.{js,vue}": ["vue-cli-service lint", "git add"]}}