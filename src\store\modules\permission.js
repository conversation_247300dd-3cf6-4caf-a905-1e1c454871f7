import { asyncRoutes, constantRoutes } from '@/router'
import { allowPermission } from '@/settings'
import { getUserMenuList } from '@/api/service'
import { HAINAN_RECORD } from '@/store/mutation-types'

/**
 * Use meta.role to determine if the current user has permission
 * @param roles
 * @param route
 */
function hasPermission(roles, route) {
  if (route.meta && route.meta.permission) {
    return roles.some(role => route.meta.permission.includes(role))
  } else {
    return true
  }
}

/**
 * Filter asynchronous routing tables by recursion
 * @param routes asyncRoutes
 * @param roles
 */
export function filterAsyncRoutes(routes, roles) {
  const res = []

  routes.forEach(route => {
    const tmp = { ...route }
    if (hasPermission(roles, tmp)) {
      if (tmp.children) {
        tmp.children = filterAsyncRoutes(tmp.children, roles)
      }
      res.push(tmp)
    }
  })

  return res
}

const state = {
  routes: [],
  addRoutes: [],
  menus: []
}

const mutations = {
  SET_ROUTES: (state, routes) => {
    state.addRoutes = routes
    // state.routes = constantRoutes.concat(routes)
    state.routes = routes
  },
  SET_MENU: (state, menus) => {
    state.menus = menus
  }
}

const actions = {
  async getMenu({ commit }, payload) {
    return new Promise((resolve, reject) => {
      if (payload.routeType === HAINAN_RECORD) {
        commit('SET_MENU', ['enterprise-record'])
        resolve({ data: [{ englishname: 'enterprise-record' }] })
      } else {
        getUserMenuList({ clientId: payload.routeType })
          .then(res => {
            if (res.success) {
              commit('SET_MENU', res.data)
              resolve(res)
            } else {
              reject(res)
            }
          })
          .catch(err => {
            reject(err)
          })
      }
    })
  },
  generateRoutes({ commit }, roles) {
    const routes = asyncRoutes
    return new Promise(resolve => {
      if (!roles) {
        resolve(routes || [])
      }
      let accessedRoutes
      if (!allowPermission) {
        accessedRoutes = routes || []
      } else {
        accessedRoutes = filterAsyncRoutes(routes, roles)
      }
      commit('SET_ROUTES', accessedRoutes)
      resolve(accessedRoutes)
    })
  },

  withInRoutes({ commit }, roles) {
    return new Promise(resolve => {
      const routes = asyncRoutes
      let accessedRoutes = []
      if (!roles) {
        accessedRoutes = routes || []
      } else {
        accessedRoutes = routes.filter(item => {
          if (roles.flag) {
            // console.log('区内')
            return roles.withInRoutes.includes(item.path)
          } else {
            // console.log('区外')
            return item.path !== ('/within/goods-off-island')
          }
        })
      }
      commit('SET_ROUTES', accessedRoutes)
      resolve(accessedRoutes)
    })
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
