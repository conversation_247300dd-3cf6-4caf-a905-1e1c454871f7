<template>
  <el-dialog width="70%" :class="[{'animated':true, 'fadeOutDown': !dialogVisible, 'fadeInDown': dialogVisible},'commonDialog']" :title="title" :visible.sync="dialogVisible" :close-on-click-modal="false" :modal-append-to-body="false" @close="close">
    <slot></slot>
    <div slot="footer" class="dialog-footer">
      <slot name="footer"></slot>
    </div>
  </el-dialog>
</template>
<script>
export default {
  name: 'PageDialog',
  props: {
    title: {
      type: String,
      default: ''
    },
    visible: {
      type: Boolean,
      default: false
    },
    width: {
      type: String,
      default: '80%'
    }
  },
  data() {
    return {
      dialogVisible: false
    }
  },
  watch: {
    visible: {
      handler(val) {
        this.dialogVisible = val
      },
      immediate: true
    }

  },
  methods: {
    close() {
      this.$emit('dialogClose')
    }
  }
}
</script>

<style scoped lang="scss">
.el-dialog__body {
  padding: 0;
  color: #606266;
  font-size: 0.14rem;
}
.dialog-footer {
  text-align: center;
}

// ::v-deep .el-dialog {
//   // margin-top: 50px!important;
//   height: auto;
//   height: 75vh;
//   overflow-y: auto;
// }
// ::v-deep .el-dialog__body {
//   height: 60vh !important;
// }
</style>
