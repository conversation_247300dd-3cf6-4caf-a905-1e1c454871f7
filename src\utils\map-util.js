export const CSDM_JGHG = {
  TS6401S002: '6401',
  TS6407S005: '6407',
  TS6401S001: '6401',
  TS6401S003: '6401',
  TS6404S001: '6404',
  TS6404S002: '6404',
  TS6404S003: '6404',
  TS6404S005: '6404',
  TS6404S006: '6404',
  TS6404S004: '6404',
  TS6404S008: '6404',
  TS6404S009: '6404',
  TS6404S007: '6404',
  TS6404S010: '6404',
  TS6413S001: '6413',
  TS6416S001: '6416',
  TS6402S001: '6402',
  TS6403S001: '6403',
  TS6403S002: '6403',
  TS6406S001: '6406',
  TS6410S001: '6410',
  TS6410S002: '6410',
  TS6410S003: '6410',
  TS6404S011: '6404',
  TS6401S004: '6401',
  TS6401S005: '6401',
  'TS6401S002-6453': '6401',
  'TS6401S002-6455': '6401',
  
}

export const GQDM_YSFS = {
  6400: '9',
  6401: '2',
  6402: '2',
  6403: '2',
  6404: '2',
  6405: '2',
  6406: '2',
  6407: '5',
  6408: '2',
  6409: '2',
  6410: '2',
  6411: '2',
  6412: '2',
  6413: '5',
  6414: '2',
  6415: '2',
  6416: '5'
}

export const code2name = (dictType, value) => {
  const dict = localStorage.getItem(dictType)
  if (!dict) {
    console.error(`${dictType}字典不存在`)
    return ''
  }
  const obj = JSON.parse(dict)
  return obj.find((item) => item.value === value)?.label || ''
}

export const name2code = (dictType, label) => {
  const dict = localStorage.getItem(dictType)
  if (!dict) {
    console.error(`${dictType}字典不存在`)
    return ''
  }
  const obj = JSON.parse(dict)
  return obj.find((item) => item.label === label)?.value || ''
}
