<template>
  <!-- 选择单价等字段 -->
  <el-dialog
    title="选择账册清单"
    :visible.sync="dialogVisible"
    width="75%"
    :append-to-body="true"
    :close-on-click-modal="false"
  >
    <div>
      <el-form :inline="true" :model="form" class="demo-form-inline">
        <el-form-item label="账册清单编号">
          <el-input v-model="form.bondInvtNo" style="width: 190px" size="small" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="申报日期" label-width="80px">
          <el-date-picker
            v-model="form.dDate"
            type="daterange"
            style="width: 200px"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="币制" label-width="60px">
          <el-select v-model="form.currency" style="width: 190px" filterable size="small" placeholder="请选择">
            <el-option v-for="(item, index) in currency" :key="index" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item class="fr">
          <el-button type="primary" class="h-32" size="mini" @click="getList()">查询</el-button>
          <el-button size="mini" class="h-32" @click="onReset()">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div>
      <el-table
        :data="accountTableData"
        v-loading="loading"
        border
        style="width: 100%"
        highlight-current-row
        @row-click="handleChange"
        @sort-change="handleSortChange"
      >
        <el-table-column width="55px">
          <template slot-scope="scope">
            <el-radio
              v-model="selectedRow"
              class="table-radio"
              tabindex="-1"
              aria-hidden="true"
              :label="scope.$index"
            />
          </template>
        </el-table-column>
        <el-table-column prop="ddate" label="申报日期" min-width="140" />
        <el-table-column prop="productName" label="商品名称" min-width="100" />
        <el-table-column prop="bondInvtNo" label="账册清单编号" min-width="140" />
        <el-table-column prop="vlTypes" label="料件来源类型" min-width="100" />
        <el-table-column prop="price" label="单价" min-width="80" />
        <el-table-column prop="currency" label="币制" min-width="80">
          <template slot-scope="scope">
            {{ code2name('currency_code', scope.row.currency) }}
          </template>
        </el-table-column>
        <el-table-column prop="priceRmb" label="申报人民币单价" min-width="120" />
        <el-table-column prop="originCountry" label="原产国(地区)" min-width="120">
          <template slot-scope="scope">
            {{ code2name('country_code', scope.row.originCountry) }}
          </template>
        </el-table-column>
        <el-table-column prop="isFocusProduct" label="是否重点商品" min-width="120">
          <template slot-scope="scope">
            {{ code2name('is_focus_product', scope.row.isFocusProduct) }}
          </template>
        </el-table-column>
        <!-- <el-table-column prop="uncoVersion" label="单耗版本号" min-width="120" /> -->
      </el-table>
      <div class="pagination">
        <el-pagination
          background
          small
          :current-page="accountPagination.pageNo"
          :page-sizes="[10, 20, 30, 40]"
          :page-size="accountPagination.pageSize"
          layout="total, prev, pager, next,sizes, jumper"
          :total="accountTotal"
          @size-change="accountSizeChange"
          @current-change="accountCurrentChange"
        />
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button class="h-32" type="primary" @click="selectAccountNo">确 定</el-button>
      <el-button class="h-32" @click="dialogVisible = false">取 消</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { execute } from '@/api/api'
import moment from 'moment'
import { code2name } from '@/utils/map-util'

export default {
  name: 'SelectDialog',
  props: {
    row: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      code2name,
      loading: false,
      typeMap: {
        0: '进口料件',
        1: '金二结转',
        2: '账册结转',
        3: '-',
        4: '-',
        5: '-',
        6: '账册调整'
      },
      dialogVisible: false,
      form: {
        bondInvtNo: '', //
        dDate: '',
        currency: ''
      },
      accountPagination: {
        page: 1,
        pageSize: 10
      },
      accountTotal: 0,
      currency: [],
      selectedRow: '',
      activeObj: {},
      sortParams: {},
      accountTableData: []
    }
  },
  mounted() {
    this.getCurrency()
    // this.getList()
  },
  watch: {
    dialogVisible: {
      handler(newVal) {
        if (newVal) {
          this.getList()
        }
      },
      deep: true
    }
  },
  methods: {
    onReset() {
      this.form = {
        bondInvtNo: '',
        dDate: [],
        currency: ''
      }
      this.getList()
    },
    handleSortChange(column, prop, order) {
      const keyMap = {
        price: 'priceSort',
        ddate: 'dDateSort',
        priceRmb: 'priceRmbSort'
      }
      const valueMap = {
        descending: 'desc',
        ascending: 'asc'
      }
      this.sortParams = {
        key: keyMap[column.prop],
        value: valueMap[column.order]
      }
      this.getList()
    },
    handleChange(row, column, event) {
      this.activeObj = row
      this.selectedRow = row.index
    },
    getList() {
      this.loading = true
      const { dDate } = this.form
      console.log(this.row, 'this.row')

      const params = {
        url: '/prcsCommon/invtList',
        data: {
          ...this.form,
          dDateStart: dDate ? new Date(dDate[0]).getTime() : '',
          dDateEnd: dDate ? new Date(dDate[1]).getTime() : '',
          productNo: this.row.productNo, //'LJFHJC01',
          accountNo: this.row.accountNo, //'F641125L0068',
          ...this.accountPagination
        }
      }
      delete params.data.dDate
      params.data[this.sortParams['key']] = this.sortParams['value']
      execute(params).then((res) => {
        // res = {
        //   isSuccess: true,
        //   code: 200,
        //   data: {
        //     list: [
        //       {
        //         accountNo: 'F641125L0068',
        //         bondInvtNo: 'T64111I2504190000002',
        //         productNo: 'LJFHJC01',
        //         productName: '椰子汁',
        //         vlType: '0',
        //         price: 5.8,
        //         currency: 'USD',
        //         priceRmb: '40.6',
        //         uncoVersion: '1',
        //         isFocusProduct: '0',
        //         originCountry: 'MYS',
        //         ddate: *************
        //       }
        //     ],
        //     total: 1
        //   },
        //   msg: '操作成功',
        //   timestamp: *************
        // }
        this.loading = false
        if (res.code === 200) {
          if (res.data.list && res.data.list.length) {
            this.accountTableData = res.data.list.filter((item) => item.productNo === this.row.productNo)
            this.accountTableData = this.accountTableData.map((item, index) => {
              const obj = this.currency.find((i) => i.value === item.currency)
              return {
                ...item,
                uncoVersion: item.uncoVersion ? item.uncoVersion : '1',
                vlTypes: this.typeMap[item.vlType] || '',
                // currencys: obj ? obj.label : item.currency,
                index,
                ddate: item.ddate ? moment(item.ddate).format('YYYY-MM-DD HH:mm:ss') : ''
              }
            })
          } else {
            this.accountTableData = []
          }
          if (this.accountTableData.length === 1) {
            this.activeObj = this.accountTableData[0]
            this.selectedRow = this.activeObj.index
          }
          this.accountTotal = res.data.total
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    getCurrency() {
      const params = {
        url: '/common/dict/listData',
        data: {
          type: 'yp_currency_en'
        }
      }
      execute(params).then((res) => {
        this.currency = res.data.yp_currency_en.map((item) => {
          return {
            label: item.dictValue.trim() + '-' + item.dictLabelOrig,
            value: item.dictValue.trim()
          }
        })
      })
    },
    accountCurrentChange(page) {
      this.accountPagination.pageNo = page
      this.getList()
    },
    accountSizeChange(count) {
      this.accountPagination = { pageNo: 1, pageSize: count }
      this.getList()
    },
    selectAccountNo() {
      //   this.goodsDialogTableData[this.accountIndex].price = this.activeObj.priceRmb
      //   this.goodsDialogTableData[this.accountIndex].bondInvtNo = this.activeObj.bondInvtNo
      //   this.goodsDialogTableData[this.accountIndex].vlType = this.activeObj.vlType
      //   this.goodsDialogTableData[this.accountIndex].currency = this.activeObj.currency
      //   this.goodsDialogTableData[this.accountIndex].accountNo = this.activeObj.accountNo
      //   this.goodsDialogTableData[this.accountIndex].materialSourceType = this.activeObj.vlType
      this.selectedRow = ''
      this.$emit('change', { ...this.activeObj, rowIndex: this.row.$index })
      this.dialogVisible = false
    }
  }
}
</script>

<style lang="scss" scoped>
.pagination {
  padding: 15px 20px;
  display: flex;
  justify-content: flex-end;
  ::v-deep .el-input__inner {
    height: 22px;
  }
  ::v-deep .el-pagination__jump {
    margin: 0;
  }
  ::v-deep .el-pager {
    li {
      background-color: #fff;
      border: 1px solid #d2d4d9;
    }
  }
}
.mt-16 {
  margin-top: 16px;
}
.table-radio {
  ::v-deep .el-radio__label {
    display: none;
  }
}
</style>
