// with polyfills
import 'core-js/stable'
import 'regenerator-runtime/runtime'

import Vue from 'vue'

import 'normalize.css/normalize.css' // a modern alternative to CSS resets
import '@/styles/index.scss' // global css

import App from './App'
import store from './store'
import router from './router'
import { execute } from '@/api/api'
import { getBaseUrl } from '@/utils/env-url'
import { replaceDomain } from '@/utils/index'
import i18n from '@/lang' // internationalization
import '@/el-loader' // load element-ui component
import '@/icons' // icon
import './permission' // permission control
import '@/components/account/styles/index.scss'
import '@/assets/fonts/iconfont.css'
import * as filters from '@/filters' // global filters
// 注册全局的组件
import Trade from '@/components/account/index.js'
Vue.use(Trade)

import smComs from '@/components/smComs/index.js'
import vform from '../lib/vform/VFormDesigner.umd.min.js'
// import VFormRender from '../lib/vform/VFormRender.umd.min.js'

import '../lib/vform/VFormDesigner.css'
// import '../lib/vform/VFormRender.css'
import { initLocalCaches, initRate } from './localCashes'
Vue.use(vform) //同时注册了v-form-designer、v-form-render等组件
// Vue.use(VFormRender) //同时注册了v-form-designer、v-form-render等组件
Vue.use(smComs)

initLocalCaches() // 缓存字典值

initRate() // 缓存汇率数据

/**
 * If you don't want to use mock-server
 * you want to use MockJs for mock api
 * you can execute: mockXHR()
 *
 * Currently MockJs will be used in the production environment,
 * please remove it before going online ! ! !
 */
if (process.env.NODE_ENV === 'production') {
  const { mockXHR } = require('../mock')
  mockXHR()
}

// register global utility filters
Object.keys(filters).forEach((key) => {
  Vue.filter(key, filters[key])
})

Vue.config.productionTip = false
Vue.prototype.execute = execute
Vue.prototype.getBaseUrl = getBaseUrl
Vue.prototype.$replaceDomain = replaceDomain

new Vue({
  el: '#app',
  router,
  store,
  i18n,
  render: (h) => h(App)
})
