import request from '@/utils/request'
import { getBaseUrl } from '@/utils/env-url'
// 上传文件返回签名信息（上传文件第1步）
export function uploadFileReturnSignInfo(data) {
  return request({
    url: getBaseUrl() + '/obs/getFileUploadPolicy',
    data,
    method: 'post'
  })
}

// 更新已经上传的文件签名（上传文件第2步）
export function updateUploadedSigned(data) {
  return request({
    url: `${location.origin}/dyck-obs/`,
    data,
    method: 'post'
  })
}

// 获取预览地址
export function getPreviewUrl(data) {
  return request({
    url: getBaseUrl() + '/obs/getFileViewUrl',
    data,
    method: 'post'
  })
}

/**
 * 账册文件上传
 * */
// 上传文件返回签名信息（上传文件第1步）
export function accountUploadFileReturnSignInfo(data) {
  return request({
    url: `/obs/getFileUploadPolicy`,
    data,
    method: 'post'
  })
}

// 更新已经上传的文件签名（上传文件第2步）
export function accountUpdateUploadedSigned(data) {
  return request({
    url: `${location.origin}/dyck-obs/`,
    data,
    method: 'post'
  })
}

// 获取预览地址
export function accountGetPreviewUrl(data) {
  return request({
    url: `/obs/preview`,
    data,
    method: 'post'
  })
}
