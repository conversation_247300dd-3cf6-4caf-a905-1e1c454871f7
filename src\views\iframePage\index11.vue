<template>
  <div class="base-page">
    <iframe class="iframe" width="100%" height="100%" :src="src" frameborder="0"></iframe>
  </div>
</template>
<script>
export default {
  name: 'IframePage',
  data() {
    return {
      src: ''
    }
  },
  mounted() {
    let { src } = this.$route.query
    if (src.includes('?')) {
      src = `${src}&ticketSNO=${localStorage.getItem('ticketSNO')}`
    } else {
      src = `${src}?ticketSNO=${localStorage.getItem('ticketSNO')}`
    }
    this.src = `${src}`
  }
}
</script>
<style lang="scss" scoped>
.base-page {
  padding: 0;
  height: calc(100vh - 50px);
}
</style>
