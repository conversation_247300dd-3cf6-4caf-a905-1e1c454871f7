<template>
  <div>
    首页
  </div>
</template>

<script>
 
export default {
  name: 'Home',
  components: {
 
  },
  data() {
    return {
    }
  },
  computed: {
    
  },
  async mounted() {
    // const currentUser = JSON.parse(localStorage.getItem('currentUser'))
    // console.log(currentUser, 'currentUser')
    // this.flagArea = currentUser?.belongArea !== '01'
  },
  methods: {}
}
</script>

<style lang="scss" scoped></style>
