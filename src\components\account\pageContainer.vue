<template>
  <div class="box-card">
    <div v-if="tips?.content" class="tipsnotice">
      <i class="el-icon-warning-outline" style="color:#4d95fa;margin-right:6px"></i>{{ tips.content }} <el-link type="primary" @click="handleLink">{{ tips.linkText }}</el-link>
    </div>
    <div class="box-card-container">
      <div class="box-card-top">
        <div slot="title" />
        <div slot="btn" />
      </div>
      <div>
        <slot></slot>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    tips: {
      type: Object,
      default: function() {
        return {}
      }
    },
    title: {
      type: String,
      default: 'dd'
    },
    btn: {
      type: Boolean,
      default: false
    }
  },
  data: function() {
    return {
      return: {}
    }
  },
  methods: {
    handleLink() {
      this.$emit('handleLink')
    }
  }
}
</script>
<style scoped lang="scss">
</style>
