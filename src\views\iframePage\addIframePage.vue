<template>
  <div class="base-page">
    <iframe class="iframe" width="100%" height="100%" :src="src" frameborder="0" @load="onIframeLoad"></iframe>
  </div>
</template>
<script>
import { getToken } from '@/utils/auth'
import mixin from './mixin'
export default {
  name: 'IframePage',
  mixins: [mixin],
  props: {
    routeName: {
      type: String,
      default: ''
    },
    goods: {
      type: Array,
      default: () => []
    },
    accountInvtNo: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      src: ''
    }
  },
  mounted() {
    console.log(`getToken==${getToken()}`)
    let { routeName, accountInvtNo } = this.$route.query

    if (this.accountInvtNo) {
      accountInvtNo = this.accountInvtNo
    }
    routeName = routeName || this.routeName
    this.src = `${location.origin}/swapp/#/${routeName}?accountInvtNo=${accountInvtNo || ''}`
  },
  beforeDestroy() {
    // 清理事件监听
    window.removeEventListener('message', this.onMessage)
  }
}
</script>
<style lang="scss" scoped>
.base-page {
  padding: 0;
  height: calc(100vh - 50px);
}
</style>
