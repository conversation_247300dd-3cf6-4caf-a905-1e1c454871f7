<template>
  <el-card :class="{'card-disabled': disabled}" :padding="0" :dis-hover="true" :bordered="false">
    <template slot="header">{{ title }}</template>
    <div v-if="!disabled && displayCollapse" class="card-collapse">
      {{ !collapse ? '折叠' : '展开' }}
      <em @click.prevent="collapse = !collapse">
        <Icon :class="!collapse ? '' : 'expand'" type="ios-arrow-up" />
      </em>
    </div>
    <div v-show="!disabled && !collapse">
      <slot />
    </div>
  </el-card>
</template>

<script>
export default {
  name: 'PageCard',
  props: {
    title: {
      type: String,
      required: true
    },
    disabled: {
      type: Boolean,
      required: false,
      default: false
    },
    displayCollapse: {
      type: Boolean,
      required: false,
      default: true
    }
  },
  data() {
    return {
      collapse: false
    }
  },
  methods: {}
}
</script>

  <style lang="scss"  scoped>
.el-card {
  &.card-disabled {
    &:deep(.el-card-head) {
      background-color: #eeeeee;
      color: fade(black, 26);

      &:before {
        background-color: fade(black, 26);
      }
    }
  }

  &:deep(.el-card-head) {
    position: relative;
    padding: 8px 24px;
    //background-color: #fcfcff;
    border-bottom: none;
    color: #2b3674;
    font-size: 16px;
    font-weight: 600;
    line-height: 24px;

    &:before {
      content: '';
      position: absolute;
      top: 11px;
      bottom: 11px;
      left: 15px;
      width: 3px;
      background-color: #4657b1;
    }
  }

  &:deep(.el-card-extra) {
    top: 5px;
    right: 10px;

    .card-collapse {
      font-size: 14px;
      font-weight: 400;
      line-height: 22px;
      color: #8a92a6;

      em {
        margin-left: 6px;
        padding: 0;
        display: inline-block;
        width: 29px;
        height: 29px;
        text-align: center;
        line-height: 29px;
        border-radius: 10px;
        background-color: #f4f7fe;
        cursor: pointer;
        transition: all 0.3s;

        &:hover {
          background-color: #4657b1;

          i {
            color: #fff;
          }
        }

        i {
          transition: all 0.3s;
          font-size: 14px;
          color: fade(black, 65);

          &.expand {
            transform: rotate(-180deg);
          }
        }
      }
    }
  }
}
</style>
