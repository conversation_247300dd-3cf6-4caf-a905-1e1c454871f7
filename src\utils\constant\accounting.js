import i18n from '@/lang'

// 搜索条件
export const SEARCH_CONDITION = {
  displayBtn: true,
  displayBtnSpan: 8,
  items: [
    {
      type: 'el-input',
      label: '预录入编号',
      span: 8,
      prop: 'taxCode',
      placeholder: '请输入预录入编号'
    },
    {
      type: 'el-input',
      label: '确认编号',
      span: 8,
      prop: 'recordNo',
      placeholder: '请输入确认编号'
    },
    {
      type: 'el-select',
      label: '申报业务类型',
      span: 8,
      prop: 'releaseUnit3',
      placeholder: '请选择',
      seltype: 'el-option',
      multiple: false,
      clearable: true,
      select: []
    },
    {
      type: 'el-input',
      label: '商品名称',
      span: 8,
      prop: 'goodNa4me',
      placeholder: '请输入商品名称'
    },
    {
      type: 'el-select',
      label: '状态',
      span: 8,
      prop: 'relea3seUnit',
      placeholder: '请选择',
      seltype: 'el-option',
      multiple: false,
      clearable: true,
      select: []
    },
    {
      label: '申报日期',
      type: 'el-date-picker',
      eltype: 'daterange',
      startPlaceholder: '开始日期',
      endPlaceholder: '结束日期',
      prop: 'releasedTime',
      span: 8
    },
    {
      label: '海关审核通过日期',
      type: 'el-date-picker',
      eltype: 'daterange',
      startPlaceholder: '开始日期',
      endPlaceholder: '结束日期',
      prop: 'gmtModified',
      span: 8
    },
    {
      type: 'el-select',
      label: '变更状态',
      span: 8,
      prop: 'releaseUnit',
      placeholder: '请选择',
      seltype: 'el-option',
      multiple: false,
      clearable: true,
      select: []
    }
  ],
  field: {
    taxCode: '',
    status: '',
    goodName: ''
  }
}

// 表格列
export const TABLE_COLUMN = [
  { label: '预录入编号', prop: 'preNo', width: 150 },
  { label: '核算确认码/累计码', prop: 'recordNo', width: 100 },
  { label: '申报业务类型', prop: 'mark', width: 100 },
  { label: '商品名称', prop: 'recordName', width: 100 },
  { label: '状态', prop: 'status', width: 80 },
  { label: '变更状态', prop: 'changeStatus', width: 80 },
  { label: '申报日期', prop: 'gmtModified', width: 150 },
  { label: '海关审核通过时间', prop: 'releasedTime', width: 150 }
]
// 正面清单表单
export const FRONTCHECKLISTFORM = {
  field: {
    taxCode: '',
    goodName: '',
    id: ''
  },
  items: [
    {
      type: 'el-input',
      label: '税号序列',
      span: 24,
      prop: 'taxCode',
      placeholder: '',
      required: true
    },
    {
      type: 'el-input',
      label: '货品名称',
      span: 24,
      prop: 'goodName',
      placeholder: '',
      required: true
    },
    {
      type: 'el-input',
      label: 'id',
      span: 24,
      prop: 'id',
      placeholder: '',
      noshow: true,
      required: true
    }
  ]
}
export const PRODUCTRECORDSUBMITTABLE_EXTRA = [
  { label: i18n.t('productRecordSubmit.status'), prop: 'state', width: 150 },
  { label: i18n.t('productRecordSubmit.passTime'), prop: 'returnTime', width: 150 }
]

// 料件备案上传
export const UPLOAD_FILE = {
  field: {
    files: []
  },
  items: [
    {
      type: 'el-upload',
      label: '上传附件',
      prop: 'files',
      span: 22,
      required: true,
      accept: '.xls,.xlsx'
    }
  ]
}

