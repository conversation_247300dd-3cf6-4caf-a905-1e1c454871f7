import request from '@/utils/request'

export function getMenuList(data) {
  return request({
    url: 'menu/getMenuList',
    method: 'post',
    data
  })
}
export function addUserMenu(data) {
  return request({
    url: 'user/addUserMenu',
    method: 'post',
    data
  })
}
export function getUserMenuList(params) {
  return request({
    url: 'user/getUserMenuList',
    method: 'get',
    params
  })
}
export function getFileUploadPolicy(data) {
  return request({
    url: 'upload/getFileUploadPolicy',
    method: 'post',
    data
  })
}

/**
 * 获取文件展示路径
 *
 * @export
 * @param {*} data
 * @returns
 */
export function getFileViewUrl(params) {
  return request({
    url: '/getFileViewUrl',
    method: 'get',
    params
  })
}
