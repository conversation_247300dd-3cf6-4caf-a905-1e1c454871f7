// cover some element-ui styles

.el-breadcrumb__inner,
.el-breadcrumb__inner a {
  font-weight: 400 !important;
}

.el-upload {
  input[type='file'] {
    display: none !important;
  }
}

.el-upload__input {
  display: none;
}

.cell {
  .el-tag {
    margin-right: 0px;
  }
}

.small-padding {
  .cell {
    padding-left: 5px;
    padding-right: 5px;
  }
}

.fixed-width {
  .el-button--mini {
    padding: 7px 10px;
    min-width: 60px;
  }
}

.status-col {
  .cell {
    padding: 0 10px;
    text-align: center;

    .el-tag {
      margin-right: 0px;
    }
  }
}

// to fixed https://github.com/ElemeFE/element/issues/2461
.el-dialog {
  transform: none;
  left: 0;
  position: relative;
  margin: 0 auto;
}

// refine element ui upload
.upload-container {
  .el-upload {
    width: 100%;

    .el-upload-dragger {
      width: 100%;
      height: 200px;
    }
  }
}

// dropdown
.el-dropdown-menu {
  a {
    display: block;
  }
}

// fix date-picker ui bug in filter-item
.el-range-editor.el-input__inner {
  display: inline-flex !important;
}

// to fix el-date-picker css style
.el-range-separator {
  box-sizing: content-box;
}

// .el-message-box{
//   border: none;
//   &_title{
//     color: #ffffff;
//   }
// }

.el-input.is-disabled .el-input__inner,
.el-textarea.is-disabled .el-textarea__inner {
  color: #303133 !important;
}

.allow-create-select {
  .el-icon-arrow-up:before {
    content: '' !important;
  }
  .el-select-dropdown__empty {
    display: none !important;
  }
  .el-select-dropdown {
    border: none !important;
  }
}

.el-popover {
  text-align: center !important;
  padding: 12px !important;
}

.el-form-item__label {
  font-size: 14px!important;
}

.el-table {
  thead {
    th {
      // text-align: center;
    }
  }
  tbody {
    td {
      // text-align: center;
    }
  }
  span {
    font-size: 14px!important;
  }
  .cell {
    font-size: 14px!important;
  }
}

.el-button--mini {
  font-size: 14px!important;
}

.el-dialog__header {
  border-bottom: 1px solid rgba(0,0,0,0.06);
}

 .el-dialog__footer {
  padding: 20px;
  border-top: 1px solid rgba(0,0,0,0.06);
}

 .el-dialog__title {
  font-weight: bold!important;
}

 .el-message-box__header {
   border-bottom: 1px solid rgba(0,0,0,0.06);
 }

.el-dialog__wrapper {
  h4 {
    padding-left: 0!important;
  }
}

.callback-list {
  .el-dialog__body {
    padding-top: 10px!important;
  }
}

.callback-list {
  .el-dialog__body {
    padding-top: 10px!important;
  }
}
