@import '../../../styles/variables.scss';
.el-button--medium {
  padding: 10px;
}
.pageBetween {
  // padding: 0 16px;
  box-sizing: border-box;
  // border:1px solid #ccc;
  background-color: #fff;
  // border-radius: 10px;
}
.pad16 {
  padding: 0 16px;
  box-sizing: border-box;
}
.btn_bgActive {
  background: #d7e1ff !important;
  color: $menuActiveText !important;
}
.btn_bg {
  // background-color: #fff;
  // border: 1px solid #fff;
  color: #000;
}
.btn_bg1 {
  background: #f0f5ff;
  color: #000;
}
.btn_bg2 {
  background-color: rgba(230, 255, 251, 0.6);
  color: #000;
}
.btn_bg3 {
  background-color: rgba(255, 251, 230, 0.6);
  color: #000;
}
.btn_bg4 {
  background-color: rgba(255, 241, 240, 0.6);
  color: #000;
}
:deep().el-collapse-item__header {
  font-size: 16px !important;
  border-bottom: 2px solid #2c83e9 !important;
}
:deep().selfCollapseItem {
  .el-collapse-item__header {
    font-size: 16px !important;
    border-bottom: 0 solid #fff !important;
    position: relative;
    padding-left: 16px;
    &::before {
      content: '';
      display: block;
      width: 8px;
      height: 20px;
      border-radius: 4px;
      background-color: #fff;
      position: absolute;
      left: 0px;
      top: calc(100% - 10px) / 2;
    }
  }
}
.flex-grid4 {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
}
.flex-grid1 {
  // display: grid;
  // grid-template-columns: repeat(1, 1fr);
  margin: auto;
}
.flex-grid5 {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
}
.flex-grid6 {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
}
.norap {
  & > div {
    white-space: nowrap; /* 禁止文本换行 */
    overflow: hidden; /* 隐藏溢出的内容 */
    text-overflow: ellipsis;
  }
}
.over-ellipsis {
  white-space: nowrap; // 禁止换行
  overflow: hidden; // 隐藏溢出内容
  text-overflow: ellipsis; // 超出部分显示省略号
}
.flex-row {
  display: flex;
  flex-direction: row;
}
.flex1 {
  flex: 1;
}
.flex1_1 {
  flex: 1.2;
}
.ml10 {
  margin-left: 10px;
}
.btn_c {
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}
.flex-grid2 {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  column-gap: 16px;
  -webkit-column-gap: 16px;
}
.flex-grid3 {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  column-gap: 16px;
  -webkit-column-gap: 16px;
}
.flex-row {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
}
.account-top {
  padding: 10px 16px 0 16px;
  box-sizing: border-box;
  // margin-bottom: 16px;
  background-color: #fff;
  &-left {
    & > div:nth-child(1) {
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 20px;
      color: rgba(0, 0, 0, 0.85);
      line-height: 28px;
      text-align: left;
      font-style: normal;
    }
    & > div:nth-child(2) {
      margin-top: 8px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 14px;
      color: rgba(0, 0, 0, 0.65);
      line-height: 22px;
      text-align: left;
      font-style: normal;
    }
  }
}
.account-center {
  padding: 0 16px;
  box-sizing: border-box;
  margin-bottom: 16px;
  // height: 25vh;
}
.account-bottom {
  div {
    height: 25vh;
  }
}
.account-data {
  // height: 25vh;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  background: #ffffff;
  // margin-top: 16px;
  border-radius: 4px 4px 0px 0px;
  border: 1px solid #e9e9e9;
  position: relative;

  .accountTable {
    height: 176px;
    // overflow-y: hidden;
    .accountTable-box {
      // padding: 8px;
      height: 100%;
      overflow: hidden;
      // border: 1px solid rgba(0, 0, 0, 0.06);
      display: flex;
      flex-direction: column;
      justify-content: flex-start;
      .tableTitle {
        & > div {
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          font-size: 14px;
          color: #23262f;
          font-style: normal;
        }
      }
      .tableItem {
        // height: calc(100% / 3);
        display: flex;
        flex-direction: row;
        border-bottom: 1px solid #ebeef5;
        & > div {
          flex: 1;
          height: 44px;
          line-height: 44px;
          text-align: center;
          border-right: 1px solid #ebeef5;
          padding: 0 8px;
          box-sizing: border-box;
        }
        & > div:last-child {
          border-right: 0;
        }
      }
      .flex-btn {
        margin-top: 4%;
        height: 50%;
      }
    }
    // .tableItem {
    //   height: 1;
    //   line-height: 1;
    //   font-size: 14px;
    // }
  }
  .item-title {
    height: 44px;
    padding: 0 24px;
    background: #f2f3f5;
    border-radius: 3px 3px 0px 0px;
    .iconfont{
      color:$menuActiveText;
    }
    & > div:nth-child(1) {
      font-family: PingFangSC, PingFang SC;
      font-weight: 600;
      font-size: 16px;
      color: rgba(0, 0, 0, 0.85);
      line-height: 22px;
      text-align: left;
      font-style: normal;
    }
  }
  .accountTotal {
    // margin-top: 8px;
    font-size: 16px;
    height: 32px;
  }
}
.accountTable {
  .el-carousel {
    height: 100% !important;
  }
  .el-carousel__container {
    height: 100% !important;
    border: 1px solid #00f;
  }
  .el-carousel__arrow {
    top: 30% !important;
  }
  .el-carousel__arrow--left {
    right: 45px !important;
  }
  .el-carousel__arrow--right {
    right: 0 !important;
  }
}
.time {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: $menuActiveText!important;
  line-height: 24px;
  text-align: left;
  font-style: normal;
}
.edit {
  display: flex;
  flex-direction: row;
  align-items: center;
  .left {
    display: inline-block;
    text-align: center;
    line-height: 24px;
    vertical-align: middle;
    width: 24px;
    height: 24px;
    border-radius: 2px 0 0 2px;
    background: #fff;
    border: solid 1px rgba(0, 0, 0, 0.06);
    margin-right: 5px;
    cursor: pointer;
  }
  .right {
    display: inline-block;
    text-align: center;
    line-height: 24px;
    vertical-align: middle;
    border-radius: 0 2px 2px 0;
    width: 24px;
    height: 24px;
    background: #fff;
    border: solid 1px rgba(0, 0, 0, 0.06);
    cursor: pointer;
  }
  .right:hover,
  .left:hover {
    background: #f2f5f9;
  }
}
.pointer {
  cursor: pointer;
  text-decoration: underline;
}
.collapseTable {
  position: relative;
  .l-table {
  }
  .collapseTableContent {
    font-size: 14px;
  }
}
.nodate {
  text-align: center;
  font-size: 12px;
  color: #333;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
:deep().penk-form-container,
:deep().el-table {
  margin-top: 6px !important;
}

.highlight-row {
  background-color: #1890ff; /* 示例背景色 */
  color: #ffffff !important;
}

/* 设置当前页面element全局table 选中某行时的背景色*/
:deep(.el-table__body tr.current-row > td) {
  background: #d7e1ff !important;
  color: #fff;
}
.mainCollapse {
  border-bottom: 0px solid #fff !important;
}
:deep().el-collapse-item__content {
  padding-bottom: 0 !important;
}
:deep().el-collapse {
  border: none !important;
}
.el-collapse-item:last-child {
  margin-bottom: 0 !important;
}
