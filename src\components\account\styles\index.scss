@import '../../../styles/variables.scss';
body {
  font-size: 18px !important;
}
.main-box-card {
  padding: 16px;
  box-sizing: border-box;
}
.box-card {
  // padding: 16px;
  height: calc(100vh - 100px);
  position: relative;
  background-color: #e5e5e5;
}
.box-collapse {
  height: calc(100vh - 180px);
  overflow: hidden;
  background-color: #e5e5e5;
  .box-collapse-box {
    height: 100%;
    overflow-y: scroll;
    background-color: #e5e5e5;
  }
  .el-collapse-item {
    // padding: 16px 16px;
    // box-sizing: border-box;
  }
  .el-collapse{
    border: none!important;
  }
}
.box-collapse-box::-webkit-scrollbar {
  width: 0 !important;
}
.mainPageBetween {
  padding: 16px;
  box-sizing: border-box;
  background-color: #fff;
}
.vhowHeight {
  overflow: hidden;
}
.tipsnotice {
  width: calc(100% - 32);
  position: absolute;
  left: 16px;
  right: 16px;
  top: 10px;
  font-size: 16px;
  padding: 6px 16px;
  background-color: #d9e7fb;
  color: #000;
  z-index: 99;
}
.ml-16 {
  padding-left: 16px;
  box-sizing: border-box;
}
.box-card-container {
  height: 100%;
  overflow-y: scroll;
  // border:1px solid #f00;
}
.box-card-container::-webkit-scrollbar,
.el-drawer.ttb::-webkit-scrollbar {
  width: 0;
}
.box-card-top {
  display: flex;
  flex-direction: column;
}
.main-title {
  // padding: 10px 16px;
  box-sizing: border-box;
  // border-bottom: 1px solid #2c83e9;
  background-color: #fff;
  position: relative;
  // &::before {
  //   content: '';
  //   display: block;
  //   width: 8px;
  //   height: 20px;
  //   border-radius: 4px;
  //   background-color: #2c83e9;
  //   position: absolute;
  //   left: 10px;
  //   top: calc(100% - 10px) / 2;
  // }
  & > div {
    padding-left: 6px;
  }
}
.title {
  // padding: 10px 16px;
  box-sizing: border-box;
  // border-bottom: 1px solid #2c83e9;
  background-color: #fff;
  position: relative;
  // &::before {
  //   content: '';
  //   display: block;
  //   width: 8px;
  //   height: 20px;
  //   border-radius: 4px;
  //   background-color: #2c83e9;
  //   position: absolute;
  //   left: 0px;
  //   top: calc(100% - 10px) / 2;
  // }
}
.mainSearchForm {
  background-color: #fff;
}
.penk-form-container {
  width: 95%;
}
.el-form-item__label {
  font-weight: 400 !important;
}
.penk-form-container.searchForm {
  padding: 16px 0;
  box-sizing: border-box;
  .el-input__inner {
    background-color: #fff !important;
  }
}
.penk-form-container.editForm {
  .el-input__inner {
    background-color: rgba(24, 144, 255, 0.06) !important;
  }
}
.pageTable {
  background-color: #fff !important;
  padding: 16px;
  box-sizing: border-box;
}
.l-table {
  .el-input__inner {
    background-color: rgba(24, 144, 255, 0.06) !important;
  }
}
.el-table {
  margin-top: 16px;
}
.penk-form-container,
.el-table {
  .el-form-item__content {
    flex: 1 !important;
  }
  .el-input,
  .el-select,
  .el-textarea {
    width: 100% !important;
  }
  .el-form-item--small .el-form-item__content,
  .el-form-item--small .el-form-item__label,
  .el-form-item--middle .el-form-item__content,
  .el-form-item--middle .el-form-item__label {
    text-align: right;
    line-height: 1 !important;
  }
  .el-form-item {
    width: 100%;
    margin-bottom: 13px !important;
    display: flex;
    flex-direction: row !important;
    align-items: center !important;
  }
  .el-input.is-disabled .el-input__inner {
    background-color: #f5f7fa !important;
  }
}
.mt-10 {
  margin-top: 10px;
}
.mt-16 {
  margin-top: 16px;
}
.mb-16 {
  margin-bottom: 16px;
}
.append.el-input {
  overflow: hidden;
}
.el-input-group__append,
.el-input-group__prepend {
  background-color: #2c83e9 !important;
  padding: 0 10px !important;
  box-sizing: border-box !important;
  margin: 0 !important;
  color: #fff !important;
}

.commonDialog {
  .el-dialog__body {
    padding-top: 0 !important;
    padding-bottom: 0 !important;
  }
}
.drawer {
  top: 16px !important;
  left: 16px !important;
  right: 16px !important;
  bottom: 16px !important;
  // height: calc(100vh - 150px) !important;
  // overflow: scroll;
  .el-drawer.ttb {
    height: calc(100vh - 150px) !important;
    overflow-y: scroll;
  }
  .el-drawer__header {
    display: none;
  }
  .el-collapse-item__header {
    font-size: 16px;
  }
}
.el-button--small.is-plain {
  padding: 4px !important;
  font-size: 14px;
}

.tipsDialog {
  .el-dialog__headerbtn {
    top: 10px !important;
    .el-dialog__close.el-icon.el-icon-close {
      color: #fff;
    }
  }
  .el-dialog__header {
    background-color: #2c83e9 !important;
    padding: 10px !important;
    .el-dialog__title {
      color: #fff !important;
    }
  }

  .content,
  .el-icon-circle-check {
    font-size: 18px;
    font-weight: bold;
    color: #333;
  }
  .el-icon-circle-check {
    color: green;
    margin-right: 10px;
  }
}
.el-button--text,.el-button--text:focus, .el-button--text:hover{
  color: $menuActiveText !important;
}
.el-button--text:disabled{
  color: #999 !important;
}
.el-link.el-link--primary{
  color: $menuActiveText !important;
}
.downPointer {
  .cell > div {
    color: $menuActiveText !important;
    cursor: pointer !important;
    text-decoration: underline !important;
  }
}
.downPointer {
  color: $menuActiveText !important;
  cursor: pointer !important;
  text-decoration: underline !important;
}
// .nest-menu .el-menu-item::before {
//   width: 0 !important;
//   height: 0 !important;
// }

.el-menu.el-menu--inline {
  .nest-menu {
    .el-submenu {
      .el-menu.el-menu--inline {
        svg {
          margin-left: 10px !important;
        }
      }
    }
  }
}
.drawer-close {
  padding: 12px 16px 0 0;
  box-sizing: border-box;
  // text-align: right;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  .details-text {
    padding-top: 10px;
    padding-left: 16px;
    font-size: 16px;
    font-weight: bold;
  }
}

.dialog-search {
}
.dialog-table {
}
.dialog-footer {
  text-align: right;
}

.collapseTable .el-collapse-item__header {
  font-size: 16px !important;
}

.el-table td.is-right,
.el-table th.is-right {
  text-align: right !important;
}

.el-table__body tr.current-row > td {
  background: rgba(51,102,255, .1) !important;
  color: #333 !important;
}

.el-drawer__open .el-drawer.rtl {
  background-color: #fff;
}
.collapse {
  background-color: #fff;
  padding: 16px 0;
  box-sizing: border-box;
}


.el-pager .number{
  color: #333 !important;
}

.el-pager .number.active,.el-pager .number.active:hover{
  color: #fff !important;
  background-color: $menuActiveText !important;
}



.el-pager .number.active{
  color: $menuActiveText
}