<template>
  <section class="app-main">
    <transition name="fade-transform" mode="out-in">
      <!-- 不使用 tag view 时建议移除keepAlive -->
      <!-- <keep-alive :include="cachedViews">-->
      <keep-alive :include="cachedViews">
        <router-view v-if="key !== '/'" :key="key" />
        <div v-else class="home-page">
          <div class="bg-image"></div>
        </div>
      </keep-alive>
    </transition>
  </section>
</template>

<script>
export default {
  name: 'AppMain',
  computed: {
    cachedViews() {
      return this.$store.state.tagsView.cachedViews
    },
    key() {
      return this.$route.path
    }
  }
}
</script>

<style lang="scss" scoped>
  .bg-image {
    width: 100%;
    height: 100%;
    background-image: url("/1.1.5/static/img/home.jpg");
    // background-image: url("/static/img/home.jpg");
    background-size: cover;
    background-position:center;
  }
.app-main {
  /* 50= navbar  50  */
  min-height: calc(100vh - 50px);
  width: 100%;
  position: relative;
  overflow: hidden;
  // background: rgba(0,0,0,0.04);
  background: #e5e5e5;
}

.fixed-header + .app-main {
  padding-top: 50px;
}

.hasTagsView {
  .app-main {
    /* 90 = navbar + tags-view = 50 + 40 */
    min-height: calc(100vh - 100px);
  }

  .fixed-header + .app-main {
    padding-top: 40px;
  }
}
</style>

<style lang="scss">
// fix css style bug in open el-dialog
.el-popup-parent--hidden {
  .fixed-header {
    padding-right: 15px;
  }
}
  .home-page {
    width: 100%;
    height: calc(100vh - 90px);
    position: absolute;
  }
</style>
