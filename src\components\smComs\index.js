// 该文件负责所有的公共的组件的全局注册   Vue.use

import smInput from './smInput.vue'
import smTitle from './smTitle.vue'
import smGridTable from './smGridTable.vue'
import selectCompany from './selectCompany.vue'
import accountUpload from './AccountUpload.vue'
export default {
  // 通过install来拿到全局的Vue对象
  install(Vue) {
    //  注册全局的通用栏组件对象

    Vue.component('smInput', smInput)
    Vue.component('smTitle', smTitle)
    Vue.component('smGridTable', smGridTable)
    Vue.component('selectCompany', selectCompany)
    Vue.component('accountUpload', accountUpload)
  }
}
