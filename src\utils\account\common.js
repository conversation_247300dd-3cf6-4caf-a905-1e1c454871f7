import request from '@/utils/request'
// 用于校验规则
export function transform(val) {
  return isBlank(val) ? '' : val + ''
}

export function post(_url_, params, successFunc, errFunc) {
  return request({
    url: _url_,
    method: 'post',
    data: params
  }).then(res => {
    if (res !== false && typeof successFunc === 'function') {
      return successFunc(res)
    }
  }, res => {
    if (errFunc === 'function') {
      return errFunc(res)
    }
  })
}
/**
 * 从数据字典中获得字典  支持多个字典同时查询
 * @param dictType 字典类型
 * @param callback 回调
 * @param showValueBeforeLabel 是否在label前展示value,默认false
 */
export function getDictList(dictType, callback, showValueBeforeLabel = false) {
  let stg = window.localStorage.getItem(dictType)
  if (stg != null) {
    stg = JSON.parse(stg)
    if (typeof callback === 'function') {
      callback(stg)
    }
    return Promise.resolve(stg)
  } else {
    const arr = []
    // 缓存请求,同时多次请求同一数据字典时,只有第一次发起ajax请求,后续请求使用第一次请求获取的数据
    const deferredKey = dictType + '-deferred'
    // 防止重复请求同一数据字典
    if (window.dictLoading === undefined) {
      window.dictLoading = new Set()
      window.deferredCache = {}
    }
    if (!isBlank(dictType)) {
      if (!window.dictLoading.has(dictType)) {
        window.dictLoading.add(dictType)
        const deferred = post('/common/dict/listData', {
          type: dictType
        }, res => {
          try {
            if (!isEmpty(res.data)) {
              for (let i = 0; i < res.data.length; i++) {
                arr[i] = {
                  label: res.data[i].dictLabelOrig,
                  value: res.data[i].dictValue
                }
              }
            }
            if (typeof callback === 'function') {
              callback(arr)
            }
            if (!isEmpty(arr)) {
              window.localStorage.setItem(dictType, JSON.stringify(arr))
            }
            return arr
          } finally {
            // 清除缓存
            window.dictLoading.delete(dictType)
            delete window.deferredCache[deferredKey]
          }
        })
        window.deferredCache[deferredKey] = deferred
        return deferred
      } else {
        if (window.deferredCache[deferredKey]) {
          return Promise.resolve(window.deferredCache[deferredKey]).then(data => {
            if (typeof callback === 'function') {
              callback(data)
            }
          })
        } else {
          let stg = window.localStorage.getItem(dictType)
          if (stg != null) {
            stg = JSON.parse(stg)
            if (typeof callback === 'function') {
              callback(stg)
            }
          }
          return Promise.resolve(stg)
        }
      }
    }
  }
}

/**
 * 从数据字典中获得字典  单个字典查询
 * @param dictType 字典类型
 * @param callback 回调
 * @param showValueBeforeLabel 是否在label前展示value,默认false
 */
export function getSingleDict(dictType, callback, showValueBeforeLabel = false) {
  let stg = window.localStorage.getItem(dictType)
  if (stg != null) {
    stg = JSON.parse(stg)
    if (typeof callback === 'function') {
      callback(stg)
    }
    return Promise.resolve(stg)
  } else {
    const arr = []
    // 缓存请求,同时多次请求同一数据字典时,只有第一次发起ajax请求,后续请求使用第一次请求获取的数据
    const deferredKey = dictType + '-deferred'
    // 防止重复请求同一数据字典
    if (window.dictLoading === undefined) {
      window.dictLoading = new Set()
      window.deferredCache = {}
    }
    if (!isBlank(dictType)) {
      if (!window.dictLoading.has(dictType)) {
        window.dictLoading.add(dictType)
        const deferred = post('/common/dict/getDictList', {
          type: dictType
        }, res => {
          try {
            if (!isEmpty(res.data)) {
              for (let i = 0; i < res.data.length; i++) {
                arr[i] = {
                  label: res.data[i].dictLabelOrig,
                  value: res.data[i].dictValue
                }
              }
            }

            if (typeof callback === 'function') {
              callback(arr)
            }
            if (!isEmpty(arr)) {
              window.localStorage.setItem(dictType, JSON.stringify(arr))
            }
            return arr
          } finally {
            // 清除缓存
            window.dictLoading.delete(dictType)
            delete window.deferredCache[deferredKey]
          }
        })
        window.deferredCache[deferredKey] = deferred
        return deferred
      } else {
        if (window.deferredCache[deferredKey]) {
          return Promise.resolve(window.deferredCache[deferredKey]).then(data => {
            if (typeof callback === 'function') {
              callback(data)
            }
          })
        } else {
          let stg = window.localStorage.getItem(dictType)
          if (stg != null) {
            stg = JSON.parse(stg)
            if (typeof callback === 'function') {
              callback(stg)
            }
          }
          return Promise.resolve(stg)
        }
      }
    }
  }
}
export function isBlank(val) {
  if (val === null) {
    return true
  } else if (val === undefined) {
    return true
  }
  if (typeof val === 'string') {
    return val.trim().length === 0
  }
  return false
}
export function isEmpty(value) {
  return value == null || value === '' || value === 0 || value === [] || value === {}
}

export function getDictLabel(dictType, dictValue) {
  let label = ''
  if (isBlank(dictType) || isBlank(dictValue)) {
    return label
  }
  const dictList = window.localStorage.getItem(dictType)
  if (dictList) {
    const dictListArr = JSON.parse(dictList)
    const dictJson = dictListArr.find(e => e.value === dictValue)
    if (dictJson) {
      label = isEmpty(dictJson.label) ? '' : dictJson.label
    } else {
      console.warn(`缓存中未找到 ${dictType} 类型 ${dictValue} 的数据字典值`)
    }
  } else {
    getSingleDict(dictType)
    return isEmpty(dictValue) ? '' : dictValue
  }
  return label
}

export function formatTimestamp(timestamp) {
  if (isEmpty(timestamp)) return ''
  const date = new Date(timestamp)
  const year = date.getFullYear()
  const month = (1 + date.getMonth()).toString().padStart(2, '0') // 月份从0开始，所以需要+1
  const day = date.getDate().toString().padStart(2, '0')
  const hours = date.getHours().toString().padStart(2, '0')
  const minutes = date.getMinutes().toString().padStart(2, '0')
  const seconds = date.getSeconds().toString().padStart(2, '0')
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
}

export function formatDate(timestamp) {
  if (isEmpty(timestamp)) return ''
  const date = new Date(timestamp)
  const year = date.getFullYear()
  const month = (1 + date.getMonth()).toString().padStart(2, '0') // 月份从0开始，所以需要+1
  const day = date.getDate().toString().padStart(2, '0')
  return `${year}-${month}-${day}`
}

export function formatBigDecimal(number) {
  if (number === '' || number === 'null' || number === null || number === undefined) {
    return ''
  }

  const str = number.toString()
  if (str.indexOf('.') >= 0) {
    const parts = str.split('.')
    const rout = parts[1].replace(/\.?0+$/, '')
    return rout === '' ? parts[0] : parts[0] + '.' + rout
  } else {
    return str
  }
}

export function formatAmount(amount) {
  if (amount === '' || amount === 'null' || amount === null || amount === undefined) {
    return ''
  }

  const str = amount.toString()
  if (str.indexOf('.') >= 0) {
    const parts = str.split('.')
    const rout = parts[1].replace(/\.?0+$/, '')
    const amountLast = rout === '' ? parts[0] : parts[0] + '.' + rout
    return amountLast.replace(/\d(?=(\d{3})+\.)/g, '$&,')
  } else {
    return str.replace(/\d(?=(\d{3})+\.)/g, '$&,')
  }
}

export function toFixedNoRound(num, decimals) {
  // 将数字转换为字符串
  const numStr = num.toString()

  // 如果数字本身就是整数，且没有小数位要求，或者小数位要求为0，则直接返回
  if (!decimals || numStr.indexOf('.') === -1) {
    return numStr
  }

  // 找到小数点的位置
  const pointIndex = numStr.indexOf('.')

  // 确定需要截取的小数位长度
  const cutLength = pointIndex + decimals + 1

  // 截取字符串到指定的小数位长度
  let resultStr = numStr.slice(0, cutLength)

  // 如果截取后的字符串最后一位是小数点，则去掉小数点
  if (resultStr.slice(-1) === '.') {
    resultStr = resultStr.slice(0, -1)
  }

  return resultStr
}
