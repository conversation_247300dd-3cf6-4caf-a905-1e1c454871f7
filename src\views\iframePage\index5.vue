<template>
  <div class="base-page">
    <iframe class="iframe" width="100%" height="100%" :src="src" frameborder="0" @load="onIframeLoad"></iframe>
  </div>
</template>
<script>
import { getToken } from '@/utils/auth'
import mixin from './mixin'
export default {
  name: 'IframePage',
  mixins: [mixin],
  data() {
    return {
      src: ''
    }
  },
  mounted() {
    const hash = window.location.hash
    console.log(`getToken==${getToken()}`)
    this.src = `${location.origin}/swapp/#/secondgimportdeclnx?accountInvtNo=${this.$route.query.accountInvtNo || ''}`
  },
  beforeDestroy() {
    // 清理事件监听
    window.removeEventListener('message', this.onMessage)
  }
}
</script>
<style lang="scss" scoped>
  .base-page{
    padding: 0;
    height: calc(100vh - 50px);
  }
</style>
