import i18n from '@/lang'

// 正面清单搜索条件
export const PRODUCTRECORDSUBMIT = {
  displayBtn: true,
  displayBtnSpan: 8,
  items: [
    {
      type: 'el-input',
      label: '税号序列',
      span: 8,
      prop: 'taxCode',
      placeholder: '请输入税号序列'
    },
    {
      type: 'el-input',
      label: '设备类型',
      span: 8,
      prop: 'goodName',
      placeholder: '请输入货物名称'
    },
    {
      type: 'el-input',
      label: '发布单位',
      span: 8,
      prop: 'releaseUnit',
      placeholder: '请输入发布单位'
    },
    {
      label: '发布时间',
      type: 'el-date-picker',
      eltype: 'daterange',
      startPlaceholder: '开始日期',
      endPlaceholder: '结束日期',
      prop: 'releasedTime',
      span: 8
    },
    {
      label: '更新时间',
      type: 'el-date-picker',
      eltype: 'daterange',
      startPlaceholder: '开始日期',
      endPlaceholder: '结束日期',
      prop: 'gmtModifiedTime',
      span: 8
    }
  ],
  field: {
    taxCode: '',
    status: '',
    goodName: ''
  }
}
// 正面清单表格列
export const PRODUCTRECORDSUBMITTABLE = [
  { label: '税则号列', prop: 'taxCode', width: 200 },
  { label: '设备类型', prop: 'goodName', width: 100 },
  { label: '发布单位', prop: 'releaseUnit', width: 100 },
  { label: '更新时间', prop: 'gmtModified', width: 150 },
  { label: '发布时间', prop: 'releasedTime', width: 100 }
]
// 正面清单表单
export const FRONTCHECKLISTFORM = {
  field: {
    taxCode: '',
    goodName: '',
    id: ''
  },
  items: [
    {
      type: 'el-input',
      label: '税号序列',
      span: 24,
      prop: 'taxCode',
      placeholder: '',
      required: true
    },
    {
      type: 'el-input',
      label: '货品名称',
      span: 24,
      prop: 'goodName',
      placeholder: '',
      required: true
    },
    {
      type: 'el-input',
      label: 'id',
      span: 24,
      prop: 'id',
      placeholder: '',
      noshow: true,
      required: true
    }
  ]
}
export const PRODUCTRECORDSUBMITTABLE_EXTRA = [
  { label: i18n.t('productRecordSubmit.status'), prop: 'state', width: 150 },
  { label: i18n.t('productRecordSubmit.passTime'), prop: 'returnTime', width: 150 }
]

// 料件备案上传
export const UPLOAD_FILE = {
  field: {
    files: []
  },
  items: [
    {
      type: 'el-upload',
      label: '上传附件',
      prop: 'files',
      span: 22,
      required: true,
      accept: '.xls,.xlsx'
    }
  ]
}

