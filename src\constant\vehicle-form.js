import { Message } from 'element-ui'
import { getFileViewUrl } from '@/api/service'

export const singleWindow = {
  field: {
    industryType: '',
    enterpType: ''
  },
  items: [
    {
      type: 'el-select',
      label: '主体类型',
      span: 8,
      prop: 'enterpType',
      seltype: 'el-option',
      placeholder: '请选择',
      multiple: false,
      select: [{ label: '企业', value: '0' }, { label: '事业单位', value: '1' }],
      filterable: true,
      required: true,
      onchange
    },
    {
      type: 'el-select',
      label: '所属行业',
      span: 8,
      prop: 'industryType',
      seltype: 'el-option',
      placeholder: '请选择',
      multiple: false,
      select: [],
      filterable: true,
      required: true
    }
  ]
}
export const business = {
  field: {
    companySccd: '',
    companyIc: '',
    legalPersonName: '',
    cusLawManMobile: '',
    contacter: '',
    contactPhone: '',
    companyType: '',
    registeredCapital: '',
    establishmentDate: '',
    businessDate: '',
    registrationAuthority: '',
    address: '',
    natureBusiness: '',
    companyCode: '',
    mangPlc: '',
    companyRealTypeName: '',
    companyBelongAreaName: ''
  },
  items: [
    {
      type: 'el-input',
      label: '统一社会信用代码',
      span: 8,
      prop: 'companySccd',
      placeholder: '',
      required: true
    },
    {
      type: 'el-input',
      label: '企业名称',
      span: 8,
      prop: 'companyIc',
      placeholder: '',
      required: true
    },
    {
      type: 'el-input',
      label: '法定代表人',
      span: 8,
      prop: 'legalPersonName',
      placeholder: '',
      required: true
    },
    {
      type: 'el-input',
      label: '法定代表人联系电话',
      span: 8,
      prop: 'cusLawManMobile',
      eltype: 'tel',
      placeholder: '',
      rules: [
        { required: true, message: '法定代表人联系电话不能为空' },
        { pattern: /^\d{7,}$/, message: '联系电话格式错误', trigger: 'blur' }
      ]
    },
    {
      type: 'el-input',
      label: '联系人',
      span: 8,
      prop: 'contacter',
      placeholder: '',
      required: true
    },
    {
      type: 'el-input',
      label: '联系人联系电话',
      span: 8,
      prop: 'contactPhone',
      eltype: 'tel',
      placeholder: '',
      rules: [
        { required: true, message: '联系人联系电话不能为空' },
        { pattern: /^\d{7,}$/, message: '联系电话格式错误', trigger: 'blur' }
      ]
    },
    {
      type: 'el-input',
      label: '营业执照类型',
      span: 8,
      prop: 'companyType',
      placeholder: '',
      required: true
    },
    {
      type: 'el-input',
      label: '注册资本',
      span: 8,
      prop: 'registeredCapital',
      placeholder: '请输入注册资本，例：100万',
      required: true
    },
    {
      type: 'el-date-picker',
      label: '成立日期',
      span: 8,
      prop: 'establishmentDate',
      eltype: 'date',
      placeholder: '',
      seltype: 'el-option',
      required: true,
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now()
        }
      }
    },
    {
      type: 'el-date-picker',
      label: '营业期限',
      showLongTerm: true,
      longTerm: false,
      span: 8,
      prop: 'businessDate',
      eltype: 'date',
      placeholder: '',
      seltype: 'el-option',
      required: true,
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() < Date.now() - 8.64e7
        }
      }
    },
    // {
    //   type: 'slot',
    //   slotName: 'term',
    //   label: '营业期限',
    //   prop: 'businessDate',
    //   span: 8
    // },
    {
      type: 'el-input',
      label: '登记机关',
      span: 8,
      prop: 'registrationAuthority',
      placeholder: '',
      required: true
    },
    {
      type: 'el-input',
      label: '住所',
      span: 8,
      prop: 'address',
      placeholder: '',
      required: true
    },
    {
      type: 'el-input',
      eltype: 'textarea',
      autosize: true,
      resize: 'none',
      label: '经营范围',
      span: 8,
      prop: 'natureBusiness',
      placeholder: '',
      required: true,
      style: 'maxlength_1'
    },
    {
      type: 'el-input',
      eltype: 'textarea',
      autosize: true,
      resize: 'none',
      label: '经营场所',
      span: 8,
      prop: 'mangPlc',
      placeholder: '',
      required: true,
      style: 'maxlength_1'
    },
    {
      type: 'el-input',
      span: 8,
      prop: 'companyCode',
      label: '企业注册号（该项为海关注册编码）',
      placeholder: '',
      maxlength: 10
    },
    {
      type: 'el-input',
      span: 8,
      prop: 'companyRealTypeName',
      label: '企业类型',
      placeholder: ''
    },
    {
      type: 'el-input',
      span: 8,
      prop: 'companyBelongAreaName',
      label: '企业所属地行政区',
      placeholder: ''
    }
  ]
}
export const apply = {
  field: {
    applicationIndustry: '',
    benefitType: '0',
    // importedGoods: [],
    files: [],
    remark: ''
  },
  items: [
    {
      type: 'el-input',
      span: 8,
      prop: 'applicationIndustry',
      label: '进口设备所应用行业',
      placeholder: '',
      // noShow: true,
      showLabelTip: true,
      tip: '请填写本单位经营范围中的内容',
      required: true
    },
    {
      type: 'el-select',
      label: '享惠类型',
      span: 8,
      prop: 'benefitType',
      seltype: 'el-option',
      placeholder: '请选择',
      multiple: false,
      select: [
        {
          value: '0',
          label: '享惠生产企业'
        }
      ],
      filterable: true,
      required: true,
      disabled: true
    },
    {
      type: 'el-upload',
      label: '上传附件',
      prop: 'files',
      span: 22,
      accept: '.jpg,.jpeg,.png,.doc,.docx,.pdf ',
      uploadTip: '<div>请上传营业执照，支持 jpg、jpeg、png、doc、docx、pdf 格式，附件需小于10M</div>' +
        '<div style="font-size: 14px">提示信息：负面清单行业企业，请下载填写承诺书，并盖章后上&nbsp&nbsp<a style="color: green" href= "https://psp.singlewindow.hn.cn/dyck-obs/public/attach/imported/申报企业承诺书.docx">下载承诺书模版</a></div>',
      uploadOptions: {
        action: `${window.location.protocol}//${window.location.host}${process.env.VUE_APP_BASE_API}/upload`,
        beforeUpload: function(file) {
          if (!/.(jpe?g|png|docx?|pdf)/g.test(file.name)) {
            Message('附件上传支持 jpg、jpeg、png、doc、docx、pdf 格式')
            return false
          }
          if (file.size > 10 * 1024 * 1024) {
            Message('附件需小于10M')
            return false
          }
          return true
        },
        onPreview: function(file) {
          getFileViewUrl({ fileUrl: file.filePath, fileName: file.name })
            .then(res => {
              if (res.success) {
                window.open(res.data)
              } else {
                this.$message.error(res.message || '获取文件地址失败')
              }
            })
            .catch(() => {
              this.$message.error('获取文件地址失败')
            })
        }
      },
      required: true
    },
    {
      type: 'el-input',
      eltype: 'textarea',
      label: '备注',
      span: 23,
      prop: 'remark',
      style: 'maxlength_1',
      placeholder: '',
      elOptions: {
        autosize: { minRows: 3, maxRows: 5 }
      }
    }
  ]
}

export const governmentInstitutions = {
  field: {
    companySccd: '',
    companyIc: '',
    legalPersonName: '',
    natureBusiness: '',
    financialResources: '',
    registeredCapital: '',
    mangPlc: '',
    sponsor: '',
    registrationAuthority: '',
    businessDate: '',
    institutionsCategory: ''
  },
  items: [
    {
      type: 'el-input',
      label: '统一社会信用代码',
      span: 8,
      prop: 'companySccd',
      placeholder: '',
      required: true
    },
    {
      type: 'el-input',
      label: '单位名称',
      span: 8,
      prop: 'companyIc',
      placeholder: '',
      required: true
    },
    {
      type: 'el-input',
      label: '法定代表人',
      span: 8,
      prop: 'legalPersonName',
      placeholder: '',
      required: true
    },
    {
      type: 'el-input',
      label: '法定代表人联系电话',
      span: 8,
      prop: 'cusLawManMobile',
      eltype: 'tel',
      placeholder: '',
      rules: [
        { required: true, message: '法定代表人联系电话不能为空' },
        { pattern: /^\d{7,}$/, message: '联系电话格式错误', trigger: 'blur' }
      ]
    },
    {
      type: 'el-input',
      label: '联系人',
      span: 8,
      prop: 'contacter',
      placeholder: '',
      required: true
    },
    {
      type: 'el-input',
      label: '联系人联系电话',
      span: 8,
      prop: 'contactPhone',
      eltype: 'tel',
      placeholder: '',
      rules: [
        { required: true, message: '联系人联系电话不能为空' },
        { pattern: /^\d{7,}$/, message: '联系电话格式错误', trigger: 'blur' }
      ]
    },
    {
      type: 'el-input',
      label: '机构类别',
      span: 8,
      prop: 'institutionsCategory',
      placeholder: '',
      required: true
    },
    {
      type: 'el-input',
      label: '开办资金',
      span: 8,
      prop: 'registeredCapital',
      placeholder: '请输入开办资金（万元）',
      required: true
    },
    {
      type: 'el-date-picker',
      label: '有效期',
      span: 8,
      prop: 'establishmentDate',
      eltype: 'date',
      placeholder: '',
      seltype: 'el-option',
      required: true,
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now()
        }
      }
    },

    {
      type: 'el-date-picker',
      label: '至',
      // showLongTerm: true,
      longTerm: false,
      span: 8,
      prop: 'businessDate',
      eltype: 'date',
      placeholder: '',
      seltype: 'el-option',
      required: true,
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() < Date.now() - 8.64e7
        }
      }
    },
    {
      type: 'el-input',
      label: '登记管理机关',
      span: 8,
      prop: 'registrationAuthority',
      placeholder: '',
      required: true
    },
    {
      type: 'el-input',
      label: '住所',
      span: 8,
      prop: 'address',
      placeholder: '',
      required: true
    },
    {
      type: 'el-input',
      eltype: 'textarea',
      autosize: true,
      resize: 'none',
      label: '宗旨和业务范围',
      span: 8,
      prop: 'natureBusiness',
      placeholder: '',
      required: true,
      style: 'maxlength_1'
    },
    {
      type: 'el-input',
      label: '举办单位',
      span: 8,
      prop: 'sponsor',
      placeholder: '',
      required: true
    },
    {
      type: 'el-input',
      span: 8,
      prop: 'companyCode',
      label: '海关注册号（该项为海关注册编码）',
      placeholder: '',
      maxlength: 10
    },
    {
      type: 'el-input',
      label: '经费来源',
      span: 8,
      prop: 'financialResources',
      placeholder: '',
      required: true
    }
  ]
}
