import { execute } from './api/api'
const localCaches = [
  'currency_map',
  'duty_type',
  'hn_customs',
  'DCLCUS_TYPECD',
  'country_code',
  'CLEARANCE_MODE',
  'DCLCUS_FLAG',
  'MANAGE_RESULT',
  'GTYPE',
  'DECL_TRAF_MODE',
  'trade',
  'DTYPE_1_2',
  'DCL_TYPECD',
  'duty_mode',
  'currency_code',
  'YSMARK',
  'ACCOUNT_TYPE',
  'VL_TYPE',
  'IEFLAG',
  'ZC_STATUS',
  'yp_unit',
  'cq_busi_type',
  'cq_leave_progress',
  'cq_leave_staus',
  'ld_place_code',
  'place_code',
  'new_place_code',
  'second_place_code',
  'is_focus_product',
  'PAY_VAT_CT'
]
export function isEmpty(value) {
  return value == null || value === '' || value === 0 || value === [] || value === {}
}

export function initLocalCaches() {
  localCaches.forEach((dictType) => {
    execute({
      url: '/common/dict/listData',
      data: {
        type: dictType
      }
    }).then((res) => {
      if (res.data && !isEmpty(res.data[dictType])) {
        const arr = res.data[dictType].map((item) => ({
          label: item.dictLabelOrig,
          value: item.dictValue
        }))
        window.localStorage.setItem(dictType, JSON.stringify(arr))
      }
    })
  })
}

export function initRate() {
  execute({
    url: '/prcsCommon/exchangeRate',
    method: 'get'
  }).then((res) => {
    console.log(res)
    if (res.code === 200) {
      localStorage.setItem('exchangeRate', JSON.stringify(res.data))
    }
  })
}
