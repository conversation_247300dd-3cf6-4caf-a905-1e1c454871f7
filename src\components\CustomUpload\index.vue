<template>
  <div class="custom-upload-wrap" @click="onTest">
    <!-- 上传 -->
    <el-upload
      :class="{
        'avatar-uploader': isAvatarType
      }"
      v-bind="$attrs"
      :show-file-list="false"
      action=""
      :accept="customAccept"
      :limit="limit"
      :file-list="fileList"
      :on-exceed="uploadExceed"
      :http-request="httpRequest"
    >
      <!-- 头像 -->
      <template v-if="sceneType === sceneTypeEnum.avatar">
        <img v-if="avatarUrl" :src="avatarUrl" class="avatar" />
        <template v-else>
          <slot>
            <i class="el-icon-plus avatar-uploader-icon"></i>
          </slot>
        </template>
      </template>
      <!-- 其他 -->
      <template v-else>
        <slot>
          <el-button type="text">附件上传</el-button>
        </slot>
      </template>
    </el-upload>

    <!-- 文件列表 -->
    <div v-if="showFileList" class="file-list-wrap">
      <div v-for="(item, index) in fileList" :key="index" class="list-item">
        <div class="file-name">{{ item.name }}</div>
        <div>
          <i class="el-icon-download operate-icon" @click="onDowload(item)"></i>
          <i class="el-icon-view operate-icon" @click="onPreView(item)"></i>
          <i v-if="isEdit" class="el-icon-close operate-icon" @click="onDelete(index)"></i>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import dict from '@/utils/dict'
import { deepClone } from '@/utils/index'
import { uploadFileReturnSignInfo, updateUploadedSigned, getPreviewUrl } from '@/api/file'

// 操作类型
const operateTypeEnum = {
  view: 'view',
  edit: 'edit'
}

// 场景类型
const sceneTypeEnum = {
  pdf: 'pdf', // pdf
  img: 'img', // 图片
  avatar: 'avatar', // 头像、缩略图
  video: 'video' // 视频mp4格式
}

// 文件模型对象
const fileModel = {
  name: '',
  fileName: '',
  url: '',
  fileUrl: '',
  fileSize: '',
  filePath: ''
}

export default {
  model: {
    prop: 'value',
    event: 'input'
  },
  props: {
    // 上传的文件列表
    value: {
      type: Array,
      default: (_) => []
    },
    // 场景类型。 pdf、图片、视频
    sceneType: {
      type: String,
      default: ''
    },
    // 操作类型
    operateType: {
      type: String,
      default: operateTypeEnum.edit
    },
    // 最大允许上传个数
    limit: {
      type: Number,
      default: 99999
    },
    // 接受上传的文件类型
    accept: {
      type: String,
      default: ''
    },
    // 显示文件列表
    showFileList: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      dict,
      sceneTypeEnum,
      fileList: [] // 文件列表
    }
  },
  computed: {
    // 查看模式
    isView() {
      return this.operateType === operateTypeEnum.view
    },
    // 编辑模式
    isEdit() {
      return this.operateType === operateTypeEnum.edit
    },
    // 是否单个文件
    isSingleFile() {
      return +this.limit < 2
    },
    isAvatarType() {
      return this.sceneType === sceneTypeEnum.avatar
    },
    avatarUrl() {
      return this.isAvatarType && this.fileList[0]?.url ? this.fileList[0].url : ''
    },
    customAccept() {
      let str = this.accept
      if (this.sceneType === sceneTypeEnum.pdf) {
        str = '.pdf,.PDF'
      }
      if (this.sceneType === sceneTypeEnum.img) {
        str = '.png,.PNG,.jpg,JPG,jpeg,JPEG'
      }
      if (this.sceneType === sceneTypeEnum.video) {
        str = '.mp4'
      }
      return str
    }
  },
  watch: {
    value: {
      immediate: true,
      deep: true,
      handler() {
        this.fileList = this.value ? deepClone(this.value) : []
      }
    }
  },
  async mounted() {},
  methods: {
    async onTest() {
      console.log('fileList：', this.fileList)
    },
    async onDowload(item = {}) {
      try {
        const {
          filePath,
          fileName,
          name
        } = item
        const url = await this.requestPreViewUrl({
          filePath,
          fileName: fileName || name
        })
        if (url) {
          window.open(url, '_blank')
          // location.href = url
        }
      } catch (error) {
        console.log(error)
      }
    },
    async onPreView(item = {}) {
      try {
        const {
          filePath
        } = item
        const url = await this.requestPreViewUrl({
          filePath
        })
        url && window.open(url, '_blank')
      } catch (error) {
        console.log(error)
      }
    },
    onDelete(index) {
      this.fileList.splice(index, 1)
      this.$emit('input', this.fileList)
    },
    uploadExceed(files, fileList) {
      if (files.length) {
        this.$message.error(`支持附件上传最多${fileList.length}个`)
      }
    },
    updateFileList(arr) {
      this.fileList = arr
      const fileList = arr.map(item => {
        const obj = {}
        const keys = Object.keys(fileModel)
        keys.forEach(key => {
          obj[key] = item[key]
        })
        return obj
      })
      this.$emit('input', fileList)
    },
    validate(file) {
      let valid = true
      const fileSize = file.size
      if (fileSize > 10 * 1024 * 1024) {
        this.$message.error('文件需小于10MB')
        valid = false
      }
      return valid
    },

    // 请求
    async requestPreViewUrl(params) {
      let url = ''
      try {
        const res = await getPreviewUrl(params)
        if (res.isSuccess) {
          url = res.data
        } else {
          this.$message.error(res.msg || '获取文件地址失败')
        }
      } catch (error) {
        this.$message.error('获取文件地址失败')
        console.log(error)
      }
      return url
    },
    async requestUploadReturnSignInfo(file) {
      const type = file?.name?.split('.')?.[1] || ''
      let data = {}
      try {
        const params = {
          tenantId: 'sdqysp',
          contentType: file.type,
          fileType: type || 'pdf'
        }
        const res = await uploadFileReturnSignInfo(params)
        if (res.isSuccess) {
          data = res.data || {}
          data.file = file
        }
      } catch (error) {
        console.log(error)
      }
      return data
    },
    async requestUpdateUploadedSigned(signInfo = {}) {
      try {
        const {
          // dir,
          // expire,
          file,
          contentType,
          accessKeyId,
          filePath,
          // host,
          policy,
          signature
        } = signInfo
        const formData = new FormData()
        formData.append('key', filePath)
        formData.append('x-obs-acl', 'public-read')
        formData.append('content-type', contentType)
        formData.append('policy', policy)
        formData.append('AWSAccessKeyId', accessKeyId)
        formData.append('Signature', signature)
        formData.append('file', file)
        //
        await updateUploadedSigned(formData)
        const fileUrl = await this.requestPreViewUrl({ filePath })
        const fileName = file.name
        const fileSize = file.size
        const obj = {
          ...fileModel,
          name: fileName,
          fileName,
          url: fileUrl,
          fileUrl,
          fileSize,
          filePath
        }
        let arr = this.fileList
        if (this.isSingleFile) {
          arr = [obj]
        } else {
          arr.push(obj)
        }
        this.updateFileList(arr)
      } catch (error) {
        this.$message.error(error.message || '上传文件失败！')
        console.log(error)
      }
    },
    async httpRequest(fileInfo) {
      console.log('httpRequest fileInfo:', fileInfo)
      const file = fileInfo.file
      if (!this.validate(file)) {
        return
      }
      try {
        const signInfo = await this.requestUploadReturnSignInfo(file)
        await this.requestUpdateUploadedSigned(signInfo)
      } catch (error) {
        console.log(error)
      }
    },

    // 提供外部调用
    dowloadFile(params) {
      this.onDowload(params)
    },
    previewFile(params) {
      this.onPreView(params)
    }
  }
}
</script>

<style lang="scss" scoped>
.full-width {
  width: 100%;
}
.custom-upload-wrap {
  display: flex;
  flex-direction: column;
}

.file-list-wrap {
  display: flex;
  flex-direction: column;
  .list-item {
    display: flex;
    margin-top: 10px;
    padding: 8px 10px;
    line-height: 22px;
    background: #eeeeee;
    border-radius: 4px;
    .file-name {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 200px;
      padding-right: 10px;
      color: rgb(64, 158, 255);
    }
    .operate-icon {
      margin-left: 8px;
      cursor: pointer;
      color: #409eff;

      &:hover {
        font-weight: 700;
      }
    }
  }
}

// 头像
::v-deep .avatar-uploader {
  .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }
  .el-upload:hover {
    border-color: #409eff;
  }
  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    line-height: 178px;
    text-align: center;
  }
  .avatar {
    width: 178px;
    height: 178px;
    display: block;
  }
}
</style>
