<template>
  <div class="no-permission">
    <div class="top">
      <img width="750" height="320" src="@/assets/images/star.png" alt="" />
    </div>
    <div class="bottom">
      <div>抱歉，{{ userName }}</div>
      <div>本企业为不良信用企业，不具备分批出岛、集中报关资格</div>
    </div>
    <p style="margin: 48px 0; font-size: 16px; color: #666">
      根据<el-button type="text">《海南自由贸易港“二线口岸”通关信用管理若干规定》</el-button
      >，高级白名单企业、白名单企业具备分批出岛、集中报关资格，其他信用等级的企业暂无此权限。
    </p>
    <div class="btn-group">
      <el-button type="primary" @click="handleCredit">查看更多企业信用情况</el-button>
    </div>
    <el-dialog title="信用档案及画像" center class="credit-dialog" :visible.sync="creditVisible" width="1300px">
      <div class="content">
        <iframe width="100%" style="height: 100vh" :src="creditUrl" />
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button size="small" @click="creditVisible = false">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import { execute } from '@/api/api'
export default {
  data() {
    return {
      creditVisible: false,
      creditUrl: ''
    }
  },
  computed: {
    userName() {
      return this.$store.state.user.user.belongCompanyName
    }
  },
  methods: {
    handleCredit() {
      this.creditVisible = true
      const params = {
        url: '/credit/getCreditMenuUrl/myCredit',
        data: {}
      }
      execute(params).then((res) => {
        if (res.code === 200) {
          this.creditUrl = res.data
        } else {
          this.$message.error(res.msg)
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.no-permission {
  width: 100%;
  height: calc(100vh - 90px);
  background: #fff;
  padding: 18px;
  text-align: center;
  font-size: 24px;
}
.bottom {
  margin-top: 24px;
  font-weight: 900;
  div {
    margin-top: 12px;
  }
}
</style>
