<template>
  <!-- 选择单价等字段 -->
  <el-dialog
    title="料件信息"
    :visible.sync="dialogVisible"
    width="75%"
    :append-to-body="true"
    :close-on-click-modal="false"
  >
    <el-dialog width="75%" title="选择单价" :visible.sync="innerVisible" append-to-body>
      <div>
        <el-form :inline="true" :model="form" class="demo-form-inline">
          <el-form-item label="账册清单编号">
            <el-input v-model="form.bondInvtNo" style="width: 190px" size="small" placeholder="请输入" />
          </el-form-item>
          <el-form-item label="申报日期" label-width="80px">
            <el-date-picker
              v-model="form.dDate"
              type="daterange"
              style="width: 200px"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item label="币制" label-width="60px">
            <el-select v-model="form.currency" style="width: 190px" filterable size="small" placeholder="请选择">
              <el-option v-for="(item, index) in currency" :key="index" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item class="fr">
            <el-button type="primary" class="h-32" size="mini" @click="getList()">查询</el-button>
            <el-button size="mini" class="h-32" @click="onReset()">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
      <div>
        <el-table
          v-loading="loading"
          :data="accountTableData"
          border
          style="width: 100%"
          highlight-current-row
          @row-click="handleChange"
        >
          <el-table-column width="55px" align="center">
            <template slot-scope="scope">
              <el-radio
                v-model="selectedRow"
                class="table-radio"
                tabindex="-1"
                aria-hidden="true"
                :label="scope.$index"
              />
            </template>
          </el-table-column>
          <el-table-column prop="ddate" label="申报日期" min-width="140" align="center" />
          <el-table-column prop="productName" label="商品名称" min-width="100" align="center" />
          <el-table-column prop="bondInvtNo" label="账册清单编号" min-width="140" align="center" />
          <el-table-column prop="vlType" label="料件来源类型" min-width="100" align="center">
            <template slot-scope="scope">
              {{ code2name('VL_TYPE', scope.row.vlType) }}
            </template>
          </el-table-column>
          <el-table-column prop="price" label="单价" min-width="80" align="center" />
          <el-table-column prop="currency" label="币制" min-width="80" align="center">
            <template slot-scope="scope">
              {{ code2name('currency_code', scope.row.currency) }}
            </template>
          </el-table-column>
          <el-table-column prop="priceRmb" label="申报人民币单价" min-width="120" align="center" />
        </el-table>
        <div class="pagination">
          <el-pagination
            background
            small
            :current-page="accountPagination.pageNo"
            :page-sizes="[10, 20, 30, 40]"
            :page-size="accountPagination.pageSize"
            layout="total, prev, pager, next,sizes, jumper"
            :total="accountTotal"
            @size-change="accountSizeChange"
            @current-change="accountCurrentChange"
          />
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button class="h-32" type="primary" @click="confirmPrice">确 定</el-button>
        <el-button class="h-32" @click="innerVisible = false">取 消</el-button>
      </span>
    </el-dialog>

    <div>
      <el-table
        v-loading="loading"
        :data="infoTableData"
        border
        style="width: 100%"
        highlight-current-row
        @row-click="handleInfoChange"
      >
        <el-table-column width="55px">
          <template slot-scope="scope">
            <el-radio
              v-model="selectedInfoRow"
              class="table-radio"
              tabindex="-1"
              aria-hidden="true"
              :label="scope.$index"
            />
          </template>
        </el-table-column>
        <el-table-column prop="gdsSeqno" label="商品序号" min-width="100" />
        <el-table-column prop="productNo" label="商品备案编号" min-width="100" />
        <el-table-column prop="productName" label="商品名称" min-width="100" />
        <el-table-column prop="codeTs" label="HS商品编码" min-width="100" />
        <el-table-column prop="originCountry" label="原产国(地区)" min-width="120">
          <template slot-scope="scope">
            {{ code2name('country_code', scope.row.originCountry) }}
          </template>
        </el-table-column>
        <el-table-column prop="isFocusProduct" label="是否重点商品" min-width="120">
          <template slot-scope="scope">
            {{ code2name('is_focus_product', scope.row.isFocusProduct) }}
          </template>
        </el-table-column>
        <el-table-column prop="payVatCt" label="是否已缴增值税消费税" min-width="120">
          <template slot-scope="scope">
            {{ code2name('PAY_VAT_CT', scope.row.payVatCt) }}
          </template>
        </el-table-column>
        <el-table-column prop="cqty" label="可用库存" min-width="120" />
        <el-table-column prop="price" label="单价" min-width="80">
          <template slot-scope="scope">
            <el-button type="text" size="mini" @click="handlePrice(scope)">{{ scope.row.price || '请选择' }}</el-button>
          </template>
        </el-table-column>
        <!-- <el-table-column prop="currency" label="币制" min-width="80" />
        <el-table-column prop="priceRmb" label="申报人民币单价" min-width="120" /> -->
      </el-table>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button class="h-32" type="primary" @click="selectAccountNo">确 定</el-button>
      <el-button class="h-32" @click="dialogVisible = false">取 消</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { execute } from '@/api/api'
import moment from 'moment'
import { code2name } from '@/utils/map-util'

export default {
  name: 'SelectDialog',
  props: {
    row: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      selectRow: {},
      activePriceIndex: -1, // 单价选中行
      innerVisible: false, // 内层弹窗
      code2name,
      loading: false,

      dialogVisible: false,
      form: {
        bondInvtNo: '', //
        dDate: '',
        currency: ''
      },
      accountPagination: {
        page: 1,
        pageSize: 10
      },
      accountTotal: 0,
      currency: [],
      selectedRow: '',
      selectedInfoRow: '',
      activeObj: {},
      activeInfoObj: {},
      accountTableData: [],
      infoTableData: []
    }
  },
  watch: {
    dialogVisible: {
      handler(newVal) {
        if (newVal) {
          this.getInfoList()
        }
      },
      deep: true
    }
  },
  mounted() {
    this.getCurrency()
  },
  methods: {
    confirmPrice() {
      if (this.activePriceIndex === -1) {
        this.$message.error('请选择单价')
        return
      }
      if (Object.keys(this.activeObj).length === 0) {
        this.$message.error('请选择单价')
        return
      }
      console.log(this.activeObj, 'activeObj')

      this.infoTableData = this.infoTableData.map((item, index) => {
        if (index === this.activePriceIndex) {
          this.activeInfoObj.price = item.price
          this.activeInfoObj.priceRmb = item.priceRmb
          this.activeInfoObj.currency = item.currency
          item.price = this.activeObj.price
          item.priceRmb = this.activeObj.priceRmb
          item.currency = this.activeObj.currency
        }
        return item
      })
      this.selectedRow = ''
      this.activeObj = {}
      this.innerVisible = false
    },

    handlePrice(scope) {
      this.selectRow = scope.row
      this.getList()
      this.innerVisible = true
      this.activePriceIndex = scope.$index
      console.log(scope, 'scope')
    },
    onReset() {
      this.form = {
        bondInvtNo: '',
        dDate: [],
        currency: ''
      }
      this.getList()
    },

    handleChange(row, column, event) {
      this.activeObj = row
      this.selectedRow = row.index
    },
    handleInfoChange(row, column, event) {
      this.activeInfoObj = row
      this.selectedInfoRow = row.index
    },
    getList() {
      this.loading = true
      const { dDate } = this.form
      const params = {
        url: '/prcsCommon/invtList',
        data: {
          ...this.form,
          productNo: this.selectRow.productNo,
          originCountry: this.selectRow.originCountry,
          isFocusProduct: this.selectRow.isFocusProduct,
          payVatCt: this.selectRow.payVatCt,
          dDateStart: dDate ? new Date(dDate[0]).getTime() : '',
          dDateEnd: dDate ? new Date(dDate[1]).getTime() : '',
          productNo: this.row.productNo, // 'LJFHJC01',
          accountNo: this.row.accountNo, // 'F641125L0068',

          ...this.accountPagination
        }
      }
      delete params.data.dDate
      execute(params).then((res) => {
        this.loading = false
        if (res.code === 200) {
          if (res.data.list && res.data.list.length) {
            this.accountTableData = res.data.list.filter((item) => item.productNo === this.row.productNo)
            this.accountTableData = this.accountTableData.map((item, index) => {
              const obj = this.currency.find((i) => i.value === item.currency)
              return {
                ...item,
                uncoVersion: item.uncoVersion ? item.uncoVersion : '1',
                // vlTypes: this.typeMap[item.vlType] || '',
                // currencys: obj ? obj.label : item.currency,
                index,
                ddate: item.ddate ? moment(item.ddate).format('YYYY-MM-DD HH:mm:ss') : ''
              }
            })
          } else {
            this.accountTableData = []
          }
          if (this.accountTableData.length === 1) {
            this.activeObj = this.accountTableData[0]
            this.selectedRow = this.activeObj.index
          }
          this.accountTotal = res.data.total
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    getInfoList() {
      this.loading = true
      const params = {
        url: '/accountStockRecord/list',
        data: {
          pageNo: 1,
          pageSize: 100,
          productNo: this.row.productNo, //'LJFHJC01',
          accountNo: this.row.accountNo //'F641125L0068'
        }
      }
      execute(params).then((res) => {
        this.loading = false
        if (res.code === 200) {
          if (res.data) {
            this.infoTableData = res.data.list
            this.infoTableData = this.infoTableData.map((item, index) => {
              return {
                ...item,
                price: '',
                cqty: item.cqty || 0,
                index
              }
            })
          } else {
            this.infoTableData = []
          }
          // if (this.infoTableData.length === 1) {
          //   this.activeObj = this.infoTableData[0]
          //   this.selectedRow = this.activeObj.index
          // }
          // this.accountTotal = res.data.total
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    getCurrency() {
      const params = {
        url: '/common/dict/listData',
        data: {
          type: 'yp_currency_en'
        }
      }
      execute(params).then((res) => {
        this.currency = res.data.yp_currency_en.map((item) => {
          return {
            label: item.dictLabelOrig,
            value: item.dictValue.trim()
          }
        })
      })
    },
    accountCurrentChange(page) {
      this.accountPagination.pageNo = page
      this.getList()
    },
    accountSizeChange(count) {
      this.accountPagination = { pageNo: 1, pageSize: count }
      this.getList()
    },
    selectAccountNo() {
      if (Object.keys(this.activeInfoObj).length === 0) {
        this.$message.error('请选择料件信息')
        return
      }
      if (!this.activeInfoObj.price) {
        this.$message.error('请选择单价')
        return
      }
      this.$emit('change', { ...this.activeInfoObj, rowIndex: this.row.$index })
      this.dialogVisible = false
      this.selectedInfoRow = ''
      this.activeInfoObj = {}
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-table thead th {
    background: #f6f7f9;
    border-bottom: 1px solid #dcdee3;
    border-right: 1px solid #dcdee3;
    text-align: center;
    padding: 8px 0;
    color: #000;
}
.pagination {
  padding: 15px 20px;
  display: flex;
  justify-content: flex-end;
  ::v-deep .el-input__inner {
    height: 22px;
  }
  ::v-deep .el-pagination__jump {
    margin: 0;
  }
  ::v-deep .el-pager {
    li {
      background-color: #fff;
      border: 1px solid #d2d4d9;
    }
  }
}
.mt-16 {
  margin-top: 16px;
}
.table-radio {
  ::v-deep .el-radio__label {
    display: none;
  }
}
</style>
