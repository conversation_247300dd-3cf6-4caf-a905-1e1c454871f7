<template>
  <page-drawer
    ref="pageDrawer"
    :visible="visible"
    :form-bl-id-head="detailId"
    :form-option="formDetailsOption"
    :form-data="formData"
    :form-option1="formDetailsOption2"
    :form-data1="formData2"
    :form-option2="formDetailsOption1"
    :form-data2="formData1"
    :title="'详情'"
    :show-change-tip="true"
    @update:visible="updateVisible"
  ></page-drawer>
</template>

<script>
import SasStockBscFormData from '@/views/account/goods-off-island/out-cd/out/stock/sas-stock-bsc/sas-stock-bsc'
import { formOption as formDetailsOption } from '@/views/account/goods-off-island/out-cd/out/stock/sas-stock-bsc/sas-stock-bsc-details-options'

// 出入库申请料件
import SasStockMaterialFormData from '@/views/account/goods-off-island/out-cd/out/stock/sas-stock-material/sas-stock-material'
import { formOption as formDetailsOption1 } from '@/views/account/goods-off-island/out-cd/out/stock/sas-stock-material/sas-stock-material-details-options'

// 出入库申请成品
import SasStockProductFormData from '@/views/account/goods-off-island/out-cd/out/stock/sas-stock-product/sas-stock-product'
import { formOption as formDetailsOption2 } from '@/views/account/goods-off-island/out-cd/out/stock/sas-stock-product/sas-stock-product-details-options'

import pageDrawer from '@/components/account/pageDrawer.vue'
import { goodsTypeEnum } from './const'

export default {
  components: {
    pageDrawer
  },

  props: {
    // 可见
    visible: {
      type: Boolean,
      default: false
    },
    // 详情id
    detailId: {
      type: String,
      default: ''
    },
    // 行数据（点击列表的行数据）
    row: {
      type: Object,
      default: _ => ({
        // 货物类型
        goodsType: ''
      })
    }
  },
  data() {
    return {
      formData: new SasStockBscFormData(this),
      formDetailsOption,
      formData1: new SasStockMaterialFormData(this),
      formDetailsOption1,
      formData2: new SasStockProductFormData(this),
      formDetailsOption2
    }
  },
  computed: {
  },
  watch: {
    visible() {
      if (this.visible) {
        this.init()
      }
    }
  },
  created() {
  },
  methods: {
    updateVisible() {
      this.$emit('update:visible', false)
    },
    init() {
      if (this.formData) {
        this.formData.disabled.formDisabled = true
      }

      for (let i = 1; i <= 7; i++) {
        const formDataKey = `formData${i}`
        if (this[formDataKey] && this[formDataKey].url) {
          this[formDataKey].disabled.formDisabled = true
        }
      }

      // 判断
      if (this.row.goodsType === goodsTypeEnum.W) {
        this.formData2.title = ''
        this.formData1.title = '出库单商品信息'
        this.formData1.queryColumn.forEach((item) => {
          if (item.key === 'uncoVersion') {
            item.hidden = true
          }
        })
      } else {
        this.formData2.title = '出库单成品信息'
        this.formData1.title = '出库单料件信息'
        this.formData1.queryColumn.forEach((item) => {
          if (item.key === 'uncoVersion') {
            item.hidden = false
          }
        })
      }

      this.$nextTick(() => {
        this.$refs.pageDrawer.modalActiveHandler()
      })
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
