<template>
  <div class="base-page">
    <div class="tipsnotice" v-if="tips?.content">
      <i class="el-icon-warning-outline" style="color: #4d95fa; margin-right: 6px"></i>{{ tips.content
      }}<el-link type="primary" @click="handleLink">{{ tips.linkText }}</el-link>
    </div>
    <iframe class="iframe iframe-tips" width="100%" height="100%" :src="src" frameborder="0" @load="onIframeLoad"></iframe>
  
    <page-dialog title="查看更多企业信用情况" :visible="linkVisible" width="65%" @dialogClose="linkVisible=false">
      <iframe width="100%" style="height: 100vh" :src="creditUrl" />
    </page-dialog>
  </div>
</template>
<script>
import { getToken } from '@/utils/auth'
import { execute } from '@/api/api'
import mixin from './mixin'
export default {
  name: 'IframePage',
  mixins: [mixin],
  data() {
    return {
      src: '',
      linkVisible: false,
      creditUrl: ''
    }
  },
  computed: {
    tips() {
      const content =
        '本企业为' +
        (this.$store.state.user.user.customCreditEvaluateResult
          ? this.$store.state.user.user.customCreditEvaluateResult
          : '未知企业') +
        '，具有分批出岛、集中报关资格。'
      const linkText = '查看更多企业信用情况'

      return { content, linkText }
    }
  },
  mounted() {
    const hash = window.location.hash
    console.log(`getToken==${getToken()}`)
    this.src = `${location.origin}/swapp/#/secondgimportqrynx?accountInvtNo=${this.$route.query.accountInvtNo || ''}`
    // this.src = `${'https://test-psp.singlewindow.hn.cn'}/swapp/#/secondgimportqrynx?accountInvtNo=${this.$route.query.accountInvtNo || ''}`
  },
  methods: {
    handleLink() {
      const params = {
        url: '/credit/getCreditMenuUrl/myCredit',
        data: {}
      }
      execute(params).then((res) => {
        if (res.code === 200) {
          this.creditUrl = res.data
          this.linkVisible = true
        } else {
          this.$message.error(res.msg)
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.base-page {
  padding: 0;
  height: calc(100vh - 50px);
}
.iframe-tips {
  margin-top: 26px;
}
.tipsnotice {
  width: calc(100% - 68px);
    position: absolute;
    left: 26px;
    right: 16px;
    top: 16px;
    font-size: 16px;
    padding: 6px 16px;
    background-color: #d9e7fb;
    color: #000;
    z-index: 99;
}
</style>
