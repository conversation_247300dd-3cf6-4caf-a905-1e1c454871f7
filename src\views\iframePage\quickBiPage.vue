<template>
  <div class="base-page">
    <iframe width="100%" height="100%" :src="src" frameborder="0"></iframe>
  </div>
</template>
<script>
import { execute } from '@/api/api'

export default {
  name: 'IframePage',
  data() {
    return {
      src: ''
    }
  },
  mounted() {
    execute({
      url: '/getQuickUiUrlList',
      method: 'post',
      data: {
        englishName: window.location.hash.substring(2)
      }
    }).then((res) => {
      if (res.status !== 200) {
        this.$message({
          type: 'error',
          message: res.msg
        })
      } else {
        this.src = res.data
      }
    })
  }
}
</script>
<style lang="scss" scoped>
  .base-page{
    padding: 0;
    height: calc(100vh - 50px);
  }
</style>
