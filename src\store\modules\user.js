// import { login, logout, getInfo } from '@/api/user'
import { getToken, setToken, removeToken, setEntToken, removeEntToken } from '@/utils/auth'
import router, { resetRouter } from '@/router'
import { getHybUserInfo, getUserInfo, login, loginHyb } from '@/api/user'
import { initHybUrl } from '@/utils/method'
import { Message } from 'element-ui'
import { execute } from '@/api/api'
const state = {
  token: getToken(),
  name: '',
  avatar: '',
  introduction: '',
  roles: [],
  user: {
    companyName: '',
    userName: '',
    belongArea: ''
  },
  applyCompany: {},
  // 试点审批用户信息
  sdspUserInfo: localStorage.getItem('sdsp-ent-pc-userInfo') ? JSON.parse(localStorage.getItem('sdsp-ent-pc-userInfo')) : {}
}

const mutations = {
  SET_APPLY_COMPANY: (state, applyCompany) => {
    state.applyCompany = applyCompany
  },
  SET_TOKEN: (state, token) => {
    state.token = token
  },
  SET_INTRODUCTION: (state, introduction) => {
    state.introduction = introduction
  },
  SET_NAME: (state, name) => {
    state.name = name
  },
  SET_AVATAR: (state, avatar) => {
    state.avatar = avatar
  },
  SET_ROLES: (state, roles) => {
    state.roles = roles
  },
  SET_USER: (state, user) => {
    state.user = { ...state.user, ...user }
  },
  SET_SDSP_USERINFO: (state, userInfo) => {
    state.sdspUserInfo = userInfo
    localStorage.setItem('sdsp-ent-pc-userInfo', JSON.stringify(userInfo))
  }
}

const actions = {
  getHybUserInfo2({ commit }, payload) {
    return new Promise((resolve, reject) => {
      localStorage.removeItem('opCompanyList')
      localStorage.removeItem('opCompanyId')
      loginHyb(payload)
        .then((res) => {
          if (res.isSuccess) {
            // commit('SET_INFO', res.data)
            // setUser(res.data)
            localStorage.setItem('userInfo', JSON.stringify(res.data))
            const developmentMode = location.origin === 'https://test-psp.singlewindow.hn.cn'
            const companySccd = developmentMode ? '9146000062000917X7' : '91460000MA5TPAAF25'
            if (res.data.companySccd === companySccd) {
              // 数贸账号，允许切换其他账号
              execute({
                url: '/user/opCompanyList',
                method: 'get'
              }).then((res) => {
                if (res.code === 200) {
                  const opCompanyList = res.data
                  localStorage.setItem('opCompanyList', JSON.stringify(opCompanyList))
                }

                console.log(res)
              })
            }
            setEntToken(res.data.token)
            resolve(res)
          } else {
            Message.error(res.msg || '获取用户信息失败')
            if (res.code === 401) {
              // removeToken()
              removeEntToken()
              initHybUrl()
            }
            reject(res)
          }
        })
        .catch((err) => {
          reject(err)
        })
    })
  },

  getHybUserInfo({ commit }, payload) {
    return new Promise((resolve, reject) => {
      getHybUserInfo(payload)
        .then((res) => {
          if (res.isSuccess) {
            // commit('SET_INFO', res.data)
            // setUser(res.data)

            // res.data.entType = '0'
            commit('SET_SDSP_USERINFO', res.data)
            localStorage.setItem('userInfo', JSON.stringify(res.data))
            setEntToken(res.data.token)
            resolve(res)
          } else {
            Message.error(res.msg || '获取用户信息失败')
            if (res.code === 401) {
              // removeToken()
              removeEntToken()
              initHybUrl()
            }
            reject(res)
          }
        })
        .catch((err) => {
          reject(err)
        })
    })
  },
  getUser({ commit }) {
    return new Promise((resolve, reject) => {
      getUserInfo()
        .then((res) => {
          if (res.code === 200) {
            console.log(res.data, 'getUserInfo222')
            commit('SET_USER', res.data)

            localStorage.setItem('currentUser', JSON.stringify(res.data))
            resolve(res)
          } else {
            reject(res)
          }
        })
        .catch((err) => {
          reject(err)
        })
    })
  },
  // user login
  login({ commit }, userInfo) {
    const { username, password, captcha } = userInfo
    return new Promise((resolve, reject) => {
      login({ loginCode: username.trim(), password: password, verifyCode: captcha })
        .then((response) => {
          if (response.code === 200) {
            const { data } = response
            commit('SET_TOKEN', data.token)
            setToken(data.token)
            router.push('/')
          } else {
            Message.error(response.msg || '登录失败')
          }
          resolve(response)
        })
        .catch((error) => {
          reject(error)
        })
      const data = { token: 'admin-token' }
      commit('SET_TOKEN', data.token)
      setToken(data.token)
      resolve()
    })
  },

  // get user info
  getInfo({ commit, state }) {
    return new Promise((resolve, reject) => {
      const data = {
        roles: ['admin'],
        introduction: 'I am a super administrator',
        avatar: 'https://wpimg.wallstcn.com/f778738c-e4f8-4870-b634-56703b4acafe.gif',
        name: 'Super Admin'
      }
      const { roles, name, avatar, introduction } = data
      commit('SET_ROLES', roles)
      commit('SET_NAME', name)
      commit('SET_AVATAR', avatar)
      commit('SET_INTRODUCTION', introduction)
      resolve(data)
      // getInfo(state.token)
      //   .then(response => {
      //     const { data } = response

      //     if (!data) {
      //       reject('Verification failed, please Login again.')
      //     }

      //     const { roles, name, avatar, introduction } = data

      //     // roles must be a non-empty array
      //     if (!roles || roles.length <= 0) {
      //       reject('getInfo: roles must be a non-null array!')
      //     }

      //     commit('SET_ROLES', roles)
      //     commit('SET_NAME', name)
      //     commit('SET_AVATAR', avatar)
      //     commit('SET_INTRODUCTION', introduction)
      //     resolve(data)
      //   })
      //   .catch(error => {
      //     reject(error)
      //   })
    })
  },

  // user logout
  logout({ commit, state, dispatch }) {
    return new Promise((resolve, reject) => {
      // logout(state.token)
      //   .then(() => {
      //     commit('SET_TOKEN', '')
      //     commit('SET_ROLES', [])
      //     removeToken()
      //     resetRouter()
      //     // reset visited views and cached views
      //     dispatch('tagsView/delAllViews', null, { root: true })
      //     resolve()
      //   })
      //   .catch(error => {
      //     reject(error)
      //   })
      commit('SET_TOKEN', '')
      commit('SET_ROLES', [])
      removeToken()
      resetRouter()
      resolve()
    })
  },

  // remove token
  resetToken({ commit }, config) {
    console.log(config)
    return new Promise((resolve) => {
      commit('SET_TOKEN', '')
      commit('SET_ROLES', [])
      if (config && config.config && (config.config.url.includes('sdqysp-server-ent') || config.config.url.includes('sp-ent-server'))) {
        removeEntToken()
      } else {
        removeToken()
      }
      resolve()
    })
  },

  // dynamically modify permissions
  async changeRoles({ commit, dispatch }, role) {
    const token = role + '-token'

    commit('SET_TOKEN', token)
    setToken(token)

    const { roles } = await dispatch('getInfo')

    resetRouter()

    // generate accessible routes map based on roles
    const accessRoutes = await dispatch('permission/generateRoutes', roles, { root: true })
    // dynamically add accessible routes
    router.addRoutes(accessRoutes)

    // reset visited views and cached views
    dispatch('tagsView/delAllViews', null, { root: true })
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
