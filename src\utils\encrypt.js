import { JSEncrypt } from 'jsencrypt'
/**
 * 通用的与后台加密key
 */
const RAS_PUBLIC_KEY =
  'MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQC1IWuYHBOt6gduw/OQcUbcROd4RpNoaqwIS+mHAbFeubMPognr0ypzlbht1hC1tWAeegVdbNZcALvToWW7tdpcytPJGh4UZxjJ2gzq823N5wQa18OvV0veg70Ju5n6Yfd9ItCBpZ5bBpjm3uxNa0WH0pyJGBgX/TtbiaaHWnbSYwIDAQAB'

// 加密
export const rsaEncrypt = value => {
  const encrypt = new JSEncrypt()
  encrypt.setPublicKey(RAS_PUBLIC_KEY)
  const str = encrypt.encrypt(value)
  return str
}
