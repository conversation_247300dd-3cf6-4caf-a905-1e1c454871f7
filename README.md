## 简介

ewtp 前端项目代码

发前请仔细阅读 readme，开发规范遵循风格指南

基于[vue-element-admin](https://panjiachen.github.io/vue-element-admin-site/zh/guide/)修改的后台管理系统模板

## 前序准备

你需要在本地安装 [node](http://nodejs.org/) 和 [git](https://git-scm.com/)。本项目技术栈基于 [ES2015+](http://es6.ruanyifeng.com/)、[vue](https://cn.vuejs.org/index.html)、[vuex](https://vuex.vuejs.org/zh-cn/)、[vue-router](https://router.vuejs.org/zh-cn/) 、[vue-cli](https://github.com/vuejs/vue-cli) 、[axios](https://github.com/axios/axios) 和 [element-ui](https://github.com/ElemeFE/element)，所有的请求数据都使用[Mock.js](https://github.com/nuysoft/Mock)进行模拟，提前了解和学习这些知识会对使用本项目有很大的帮助。

## 开发

```bash
# 安装依赖
yarn install

# 启动服务
yarn run serve
```

## 发布

```bash
# 构建测试环境
yarn run build:test

# 构建预发布环境
yarn run build:stage

# 构建生产环境
yarn run build:prod
```

## 其它

```bash
# 预览发布环境效果
yarn run preview

# 预览发布环境效果 + 静态资源分析
yarn run preview -- --report

# 代码格式检查
yarn run lint

# 代码格式检查并自动修复
yarn run lint -- --fix
```

## 风格指南

### Components

所有的`Component`文件都是以大写开头 (PascalCase)，除了 `index.vue`。

例：

- `@/components/BackToTop/index.vue`
- `@/views/example/components/Button.vue`

### Views

在`views`文件下，代表路由的`.vue`文件都使用横线连接 (kebab-case)，代表路由的文件夹也是使用同样的规则。

例：

- `@/views/error-page/index.vue`
- `@/views/home/<USER>

### JS 文件

`.js`文件都遵循横线连接 (kebab-case)，`@/store/modules`中`.js`文件可使用小驼峰命名法。

例：

- `@/utils/open-window.js`
- `@/store/modules/tagsView.js`

## 注意事项

- **若不使用 tagViews 组件，建议将 AppMain 中的 keepAlive 移除。**
- **不适用缓存的路由页面，需在路由 meta 中添加 noCache。**
- **`@/src/settings.js`文件中包含部分配置开关**
- **element-ui 组件的引入在`el-loader.js`中**

## 关于代码提交规范

参考[https://www.conventionalcommits.org](https://www.conventionalcommits.org/)

### 可自行输入规范化 commit 或执行 `yarn commit` 调用规范提交工具

Commit message 都包括三个部分：Header，Body 和 Footer

```bash
<type>(<scope>): <subject>
// 空一行
<body>
// 空一行
<footer>
```

简易提交示例：
`feat: initial commit`

Header 是必需的，Body 和 Footer 可以省略。

### Header 包括三个字段：`type`（必需）、`scope`（可选）和`subject`（必需）。

- type

  `type`用于说明 commit 的类别

  ```bash
  feat：新功能（feature）
  fix：修补bug
  docs：文档（documentation）
  style：格式（不影响代码运行的变动）
  refactor：重构（即不是新增功能，也不是修改bug的代码变动）
  test：增加测试
  chore：构建过程或辅助工具的变动
  ci：更改持续集成软件的配置文件和package中的scripts命令，例如scopes: Travis, Circle等
  build：变更项目构建或外部依赖（例如scopes: webpack、gulp、npm等）
  ```

- scope

  `scope`用于说明 commit 影响的范围

- subject

  `subject`是 commit 目的的简短描述，不超过 50 个字符

  ```bash
  以动词开头，使用第一人称现在时，比如change，而不是changed或changes
  第一个字母小写
  结尾不加句号（.）
  ```

### Body 部分是对本次 commit 的详细描述，可以分成多行

### Footer 部分只用于两种情况

- 不兼容变动

  如果当前代码与上一个版本不兼容，则 Footer 部分以`BREAKING CHANGE`开头，后面是对变动的描述、以及变动理由和迁移方法。

- 关闭 Issue

  如果当前 commit 针对某个 issue，那么可以在 Footer 部分关闭这个 issue 。




