<template>
  <div class="global-header clearfix">
    <div class="header-logo fl">
      <img src="@/assets/images/logo.png" />
    </div>
    <span class="header-title fl">{{ title }}</span>
    <div class="header-right-content fr" style="display: flex; justify-content: center; align-items: center">
      <div
        class="quick-business-index"
        style="
          cursor: pointer;
          margin-right: 20px;
          height: 30px;
          padding: 0 8px;
          display: flex;
          align-items: center;
          background: rgba(255, 255, 255, 0.1);
          border: 1px solid rgba(255, 255, 255, 0.3);
          border-radius: 6px;
        "
        @click="goQuickBusinessIndex"
      >
        <img src="@/assets/images/guid.png" alt="" style="width: 16px; height: 16px; margin-right: 4px" />
        <span style="font-size: 14px; font-weight: 500">快速开展业务指引</span>
      </div>
      <span class="user-info">
        <svg class="icon-avatar" aria-hidden="true" v-on="$listeners">
          <use xlink:href="#icon-avatar" />
        </svg>
        <el-select
          v-if="opCompanyList.length"
          v-model="opCompany"
          filterable
          class="transparent-select"
          placeholder="请选择"
          @change="onOpCompanyChange"
        >
          <el-option v-for="item in opCompanyList" :key="item.id" :label="item.companyName" :value="item.id">
          </el-option>
        </el-select>
        <span v-else class="user-name">{{ userName }}</span>
      </span>
      <div style="display: flex; align-items: center; justify-content: center; padding-bottom: 16px">
        <img v-if="level === '1'" class="header-credit1" src="@/assets/images/列表尺寸-高级认证企业.png" />
        <img v-if="level === '2'" class="header-credit2" src="@/assets/images/列表尺寸-白名单企业.png" />
        <img v-if="level === '4'" class="header-credit3" src="@/assets/images/列表尺寸-重点关注企业.png" />
      </div>

      <span class="logout" @click="logout">
        <svg-icon icon-class="logout" class-name="icon-logout"></svg-icon>
        <span class="logout-text">{{ $t('header.logout') }}</span>
      </span>
    </div>
  </div>
</template>
<script>
import { removeSession } from '@/utils/auth'
import { logout } from '@/api/user'
import defaultSetting from '@/settings'
import { execute } from '@/api/api'

export default {
  name: 'GlobalHeader',
  data() {
    const opCompanyList = localStorage.getItem('opCompanyList')
    const userInfo = localStorage.getItem('userInfo')
    let obj = {}
    if (opCompanyList) {
      const developmentMode = location.origin === 'https://test-psp.singlewindow.hn.cn'
      if (developmentMode) {
        obj = JSON.parse(opCompanyList).find((item) => item.sccd === '9146000062000917X7') || {}
      } else {
        obj = JSON.parse(opCompanyList).find((item) => item.sccd === '91460000MA5TPAAF25') || {}
      }
    }
    const opCompanyId = localStorage.getItem('opCompanyId')

    return {
      title: defaultSetting.title,
      opCompany: opCompanyId || obj.id || '',
      user: userInfo ? JSON.parse(userInfo) : {},
      opCompanyList: opCompanyList ? JSON.parse(opCompanyList) : []
    }
  },
  computed: {
    userName() {
      return this.$store.state.user.user.belongCompanyName || '' + ' ' + this.$store.state.user.user.userName
    },
    level() {
      const obj = {
        1: '高级认证企业',
        2: '白名单企业',
        3: '一般信用企业',
        4: '重点关注企业'
      }
      let result = ''
      Object.keys(obj).forEach((key) => {
        if (obj[key] === this.$store.state.user.user.customCreditEvaluateResult) {
          result = key
        }
      })
      return result
    }
  },
  mounted() {
    this.getUserInfo()
  },
  methods: {
    onOpCompanyChange(val) {
      execute({
        url: '/user/changeOpCompany',
        data: {
          id: val
        }
      }).then((res) => {
        if (res.code === 200) {
          localStorage.setItem('opCompanyId', val)
          console.log(this.$route, 'this.$route')

          if (this.$route.path === '/home') {
            this.$router.push('/redirect/home')
          } else {
            this.$router.push('/redirect' + this.$route.fullPath)
            // location.reload()
          }
        } else {
          this.$message.error(res.msg || '切换失败')
        }
      })
      console.log(val)
    },
    goQuickBusinessIndex() {
      window.open('https://hainanshumao.yuque.com/dx3yfc/gq5i5a', '_blank')
    },
    getUserInfo() {
      this.$store.dispatch('user/getUser')
    },
    logout() {
      logout()
        .then((res) => {
          if (res.code === 200) {
            removeSession()
            localStorage.removeItem('opCompanyList')
            localStorage.removeItem('opCompanyId')
            window.location.href = res.data.message
            // let backUrl = `${location.origin}/yqyc-ent/#/home`
            // if (location.origin === 'https://test-psp.singlewindow.hn.cn') {
            //   backUrl = `${location.origin}/yqyc-ent/master/index.html#/home`
            // }
            // window.location.href = `https://ucs-sso-dev.digitalhainan.com.cn/logout?backUrl=${encodeURIComponent(
            //   backUrl
            // )}`
            // window.location.href = res.data.message
            // window.location.href = 'https://test-psp.singlewindow.hn.cn/yqyc-ent-server/login/sso'
          }
        })
        .catch((e) => {
          console.log(e)
        })
        .finally(() => {
          // window.location.href = 'https://test-psp.singlewindow.hn.cn/yqyc-ent-server/login/sso'
        })
    }
  }
}
</script>
<style lang="scss" scoped>
.transparent-select {
  width: 340px;
}
/* 核心样式 - 移除所有边框和背景 */
.transparent-select :deep(.el-input__inner) {
  color: #fff;
  width: 310px;
  text-indent: 6px;
  border: 0 !important;
  background: none !important;
  box-shadow: none !important;
  padding: 0; /* 可选：移除内边距 */
}

/* 移除所有状态下的边框 */
.transparent-select :deep(.el-select .el-input.is-focus .el-input__inner),
.transparent-select :deep(.el-select .el-input__inner:hover),
.transparent-select :deep(.el-select .el-input__inner:focus) {
  border-color: transparent !important;
  box-shadow: none !important;
  background: none !important;
}

/* 移除下拉图标背景（可选） */
.transparent-select :deep(.el-input__suffix) {
  background: none !important;
}

/* 移除禁用状态样式 */
.transparent-select :deep(.is-disabled .el-input__inner) {
  background-color: transparent !important;
  border-color: transparent !important;
}
.header-credit1 {
  height: 25px;
  width: 129px;
  margin-left: 10px;
  position: relative;
  top: 6px;
}
.header-credit2 {
  height: 25px;
  width: 72px;
  margin-left: 10px;
  position: relative;
  top: 6px;
}
.header-credit3 {
  height: 25px;
  width: 104px;
  margin-left: 10px;
  position: relative;
  top: 6px;
}
.global-header {
  background-image: url('../../assets/images/banner.png');
  background-size: 400px 50px;
  background-position: bottom;
  background-repeat: no-repeat;
  position: fixed;
  top: 0;
  left: 0;
  padding: 0 15px;
  width: 100%;
  height: 60px;
  line-height: 60px;
  color: #fff;
  background-color: #3366ff;
  z-index: 1002;
  font-size: 14px;
  span {
    display: inline-block;
  }
  .header-logo {
    margin-right: 9px;
    height: 100%;
    img {
      height: 29px;
      vertical-align: middle;
    }
  }
  .header-title {
    font-size: 22px;
    font-weight: 500;
    font-family: SourceHanSansCN-Medium, SourceHanSansCN;
  }
  .user-info {
    // width: 230px;
    .icon-avatar {
      width: 18px;
      height: 18px;
      vertical-align: -4px;
    }
    .user-name {
      padding-left: 10px;
    }
  }
  .logout {
    margin-left: 20px;
    cursor: pointer;
    .icon-logout {
      height: 18px;
      vertical-align: -4px;
    }
    .logout-text {
      padding-left: 10px;
    }
  }
}
</style>
