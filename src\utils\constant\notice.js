import { BUSINESS_TYPE as TYPE } from './common'
export {
  TYPE
}
export const STATUS_TO_PUBLISH = 0
export const STATUS_PUBLISHED = 1
export const STATUS_CANCEL = 2
export const STATUS = [
  {
    label: '待发布',
    value: STATUS_TO_PUBLISH
  },
  {
    label: '已发布',
    value: STATUS_PUBLISHED
  },
  {
    label: '已作废',
    value: STATUS_CANCEL
  }
]

export const SEARCH = {
  displayBtn: true,
  displayBtnSpan: 24,
  field: {
  },
  items: [
    {
      type: 'el-input',
      label: '公告标题',
      prop: 'name',
      placeholder: '请输入',
      span: 8
    },
    {
      type: 'el-input',
      label: '发布单位',
      placeholder: '请输入',
      prop: 'releaseUnit',
      span: 8
    },
    {
      type: 'el-date-picker',
      eltype: 'daterange',
      label: '发布时间',
      startPlaceholder: '开始日期',
      endPlaceholder: '结束日期',
      prop: 'releasedTime',
      span: 8
    },
    {
      type: 'el-input',
      label: '信息来源',
      prop: 'articleSource',
      placeholder: '请输入',
      span: 8
    }
  ]
}
export const LIST = [
  { label: '公告标题', prop: 'name', width: 200 },
  { label: '发布单位', prop: 'releaseUnit', width: 200 },
  { label: '信息来源', prop: 'articleSource', width: 150 },
  { label: '发布人', prop: 'publisher', width: 100 },
  { label: '发布时间', prop: 'releasedTime', width: 200 }
]
export const EDIT = {
  labelPosition: 'top',
  field: {},
  items: [
    {
      type: 'el-input',
      label: '标题',
      prop: 'name',
      placeholder: '请输入',
      required: true,
      span: 8
    },
    {
      type: 'el-input',
      label: '信息来源',
      prop: 'articleSource',
      placeholder: '请输入',
      span: 8
    },
    {
      type: 'el-select',
      label: '业务类型',
      prop: 'type',
      span: 8,
      required: true,
      seltype: 'el-option',
      select: TYPE
    },
    {
      type: 'el-date-picker',
      eltype: 'date',
      label: '发布时间',
      prop: 'releasedTime',
      span: 8,
      disabled: true
    },
    {
      type: 'el-input',
      label: '发布单位',
      prop: 'releaseUnit',
      span: 8,
      placeholder: '请输入',
      disabled: true
    },
    {
      type: 'RichTextEditor',
      label: '公告正文',
      required: true,
      prop: 'content',
      span: 24
    },
    {
      type: 'el-upload',
      prop: 'attachmentInfo',
      label: '上传附件',
      detailsTitle: '附件信息',
      accept: '.jpg, .jpeg, .png, .JPG, .JPEG, .PNG, .doc, .docx, .pdf',
      span: 24,
      limit: 10,
      uploadTip: '请上传附件，支持 jpg、jpeg、png、doc、docx、pdf 格式，附件需小于10M'
    }
  ]
}
