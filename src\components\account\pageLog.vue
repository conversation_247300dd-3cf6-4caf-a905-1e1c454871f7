<template>
  <div style="width:100%">
    <div id="btn" class="mt-16">
      <el-row>
        <el-button icon="el-icon-document-copy" type="primary" plain size="small" @click="getList">刷新</el-button>
        <el-button icon="el-icon-document-copy" type="primary" plain size="small" @click="$emit('closeLogs')">关闭</el-button>
      </el-row>
    </div>
    <div class="mt-16">
      <page-table ref="tableLog" url="/customReturnRecord" :checkbox="false" :search-params="{busId}" height="auto" :state="state" :columns="tableObj.columns">
      </page-table>
    </div>
  </div>
</template>

<script>
import { formOption } from './log/logOptions'
import LogFormData from './log/log'
export default {
  props: {
    busId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      formOption,
      tableObj: new LogFormData(this),
      state: {
        rowIndex: -1,
        columnIndex: -1
      }
    }
  },
  mounted() {
    this.getList()
  },
  methods: {
    getList() {
      this.$refs.tableLog.getTableList()
    }
  }
}
</script>

<style>
</style>
