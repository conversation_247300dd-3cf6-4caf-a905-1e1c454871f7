.Declaration__head {
  font-size: 14px;
  line-height: 40px;
  padding-left: 16px;
  padding-right: 16px;
  background: #fff;
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  z-index: 3;
  color: #333;

  display: flex;
  justify-content: space-between;

  .next-btn.next-btn-secondary:not([disabled]),
  .next-btn.next-btn-secondary:not([disabled]).visited,
  .next-btn.next-btn-secondary:not([disabled]):link,
  .next-btn.next-btn-secondary:not([disabled]):visited {
    border-color: #006eff;
    color: #006eff;
  }
  .next-btn.next-btn-secondary.active,
  .next-btn.next-btn-secondary.hover,
  .next-btn.next-btn-secondary:active,
  .next-btn.next-btn-secondary:focus,
  .next-btn.next-btn-secondary:hover {
    color: #fff;
    background-color: #006eff;
    border-color: #006eff;
  }
  .Declaration__head-wrapper {
    font-size: 12px;
    cursor: pointer;
    font-weight: 500;
  }
  .Declaration__head-btns {
    flex: 1;
    text-align: left;
  }
  .Declaration__head-template-select {
    line-height: 24px;
    min-width: unset;
    .next-input.next-medium {
      height: 24px;
      min-width: unset;
      &:not(.next-disabled) {
        border-color: #006eff;
        color: #006eff;
        .next-input-control .next-icon {
          color: #006eff;
        }
      }
      .next-input-text-field {
        padding: 0 5px;
      }
    }
  }
  .Declaration__tyle-icon-arrow:before {
    font-size: 12px;
    color: #333;
    margin-left: 3px;
    transition: all 0.2s ease;
  }
  .Declaration__tyle-icon-arrow-open:before {
    transform: rotate(180deg);
    margin-right: 3px;
  }
}
.Declaration__container {
  padding: 20px;
  width: calc(100% + 16px);
  margin-left: -8px;
  min-height: calc(100vh - 90px);
  .next-input.next-medium input {
    padding-left: 0;
  }
  .height-5 {
    padding: 4px 10px;
  }
  .el-input-group__append {
    border: none;
    border-radius: 0;
    position: relative;
    height: 21px;
  }
  .el-table__empty-text {
    font-size: 12px;
  }
  .el-table--medium td, .el-table--medium th {
    padding: 0;
  }
}
.Declaration__content {
  width: 100%;
  min-width: 1168px;

  .single-win-form-container {
    .el-form-item {
      margin-bottom: 0;
    }
    .el-form-item__content {
      height: 20px;
      line-height: 20px;
    }
    input {
      padding: 0 3px;
      border-radius: 0;
    }
    .el-input--mini {
      height: 20px;
      line-height: 20px;
    }
    .el-input__inner {
      height: 20px;
      line-height: 20px;
      border: none;
    }
  }
}
.Declaration__content_dialog {
  width: 100%;
  .height-5 {
    padding: 4px 10px;
  }
  .single-win-form-container {
    .el-form-item {
      margin-bottom: 0;
    }
    .el-form-item__content {
      height: 20px;
      line-height: 20px;
    }
    input {
      padding: 0 3px;
      border-radius: 0;
    }
    .el-input--mini {
      height: 20px;
      line-height: 20px;
    }
    .el-input__inner {
      height: 20px;
      line-height: 20px;
      border: none;
    }
  }
}

.Declaration__content-left {
  min-width: 873px;

  .tableNodata {
    padding-top: 20px;
    padding-bottom: 20px;
  }
}
.next-dialog.commodity-Dialog {
  &.licenseDocus_dialog {
    width: 600px;
  }
  .single-win-form-container .single-win-item-label {
    width: auto;
    text-align: left;
  }
  .next-input.next-medium input {
    padding-left: 3px;
  }

  .next-dialog-body {
    border-bottom: 0;
    padding-bottom: 40px;
    height: calc(100% - 100px);
  }

  .next-dialog-footer {
    border-top: 1px solid #e4e8ee;
  }
}

.Declaration__content-right {
  //min-width: 250px;
}
.Declaration__section {
  border-radius: 6px;
}
.Declaration__Tabs {
  height: 38px;
  border-radius: 6px;
  min-width: 1168px;
  .next-tabs-pure > .next-tabs-bar {
    border-bottom: none;
    .next-tabs-nav-container .next-tabs-tab.active {
      color: #006eff;
      font-weight: 500;
    }
    .next-tabs-nav-container .next-tabs-tab {
      color: #333;
      &:before {
        border-bottom-color: #006eff;
      }
    }
  }
  .next-tabs-tab {
    margin-right: 20px;
  }
  .next-tabs.next-medium .next-tabs-tab-inner {
    padding-left: 0;
    padding-right: 0;
    padding-bottom: 10px;
    font-size: 14px;
  }
}
.Declaration__docUoload-table,
.Declaration__section-table {
  font-size: 12px;
  color: #333;
  .el-table td,
  .el-table-lock {
    border-bottom-color: #bbbbbb;
  }
  .el-table {
    margin-top: 5px;
    border-top-color: #bbbbbb;
    border-left-color: #bbbbbb;
    border-radius: 3px;
    border-right: solid 1px #bbbbbb;
    //.el-table-body:hover::-webkit-scrollbar-thumb {
    //  background-color: hsla(0,0%,60%,.4) !important;
    //}
    //.el-table-body:hover::-webkit-scrollbar {
    //  background: #fff;
    //}
  }
  //::-webkit-scrollbar {
  //  height: 0;
  //  width: 0;
  //}
  //.el-table-body::-webkit-scrollbar-thumb {
  //  background-color: transparent !important;
  //
  //}
  ::-webkit-scrollbar-track {
    background: #fff !important;
  }
  thead .el-table-header-node {
    font-weight: normal;
  }
  .el-table th:not(:last-child),
  .el-table td:not(:last-child) {
    border-right: none;
  }
  .el-table-fixed .el-table-body tr:last-child td {
    border-bottom-width: 1px;
  }
  .el-table-fixed .el-table-body tr td:last-child,
  .el-table-fixed .el-table-header tr th:last-child {
    border-right-width: 0;
  }
  &:not(.noData) .el-table-body {
    background: #e5f0ff;
  }
}
.Declaration__section-table {
  .el-table-empty {
    padding: 6px 0;
  }
  .el-table th .el-table-cell-wrapper,
  .el-table td .el-table-cell-wrapper {
    padding: 2px 6px;
    height: 19px;
    white-space: nowrap;
    .next-checkbox-wrapper.checked > .next-checkbox > .next-checkbox-inner > .next-icon:before {
      width: 14px;
      font-size: 14px;
    }
    .next-checkbox-wrapper.indeterminate > .next-checkbox > .next-checkbox-inner > .next-icon {
      margin-left: -5px;
    }
  }
  .el-table-body {
    min-height: 100px;
  }
  .el-table-row {
    height: 20px;
  }
  .next-checkbox-wrapper .next-checkbox-inner,
  .next-checkbox-wrapper input[type="checkbox"] {
    width: 14px;
    height: 14px;
  }

  // 根据felx伸缩高度
  &.table-height-flex {
    display: flex;
    flex-direction: column;
    .el-table {
      display: flex;
      flex-direction: column;
      flex: 1 1 auto;
      .el-table-inner {
        display: flex;
        flex-direction: column;
        flex: 1 1 auto;
        .el-table-body {
          flex: 1 1 auto;
        }
      }
    }
  }
}

.Declaration__docUoload-table {
  .el-table-row {
    height: 30px;
  }
  .el-table th .el-table-cell-wrapper,
  .el-table td .el-table-cell-wrapper {
    padding: 0 6px;
    height: 30px;
    line-height: 30px;
    .next-input.next-medium,
    .next-btn.next-medium {
      height: 24px;
    }
  }
  .el-table-body {
    min-height: 150px;
  }
}
.Declaration__table-highlight {
  background: #f2f3f7;
}
.Declaration__table-hidehead {
  .el-table-body {
    height: 444px;
  }
}
.Declaration__content-tip {
  font-size: 12px;
  font-weight: 400;
}
.Declaration__content-tips {
  font-size: 12px;
  background: #e5f0ff;
  border: 1px solid #bbbbbb;
  border-radius: 3px;
  flex: 1;
  padding-left: 16px;
  margin-left: 3px;
  p {
    margin-top: 0;
    margin-bottom: 0;
    line-height: 1.4;
  }
}
.declaration__docupload-btn.next-btn.next-medium {
  border: 1px dashed #bbbbbb;
  border-radius: 3px;
  color: #666666;
  height: 38px;
}
.declaration__document-tips {
  font-size: 12px;
  color: #006eff;
  line-height: 21px;
  background: rgba(0, 110, 255, 0.1);
  border-radius: 3px;
  padding: 5px 16px;
  p {
    margin-top: 4px;
    margin-bottom: 4px;
  }
}
.declaration__document-list {
  .el-table-header-inner {
    span[role="required"] {
      &::before {
        content: "*";
        color: red;
      }
    }
  }
}
.declaration__document-upload {
  display: inline-block;

  .next-btn.next-btn-normal,
  .next-btn.next-btn-normal.visited,
  .next-btn.next-btn-normal:link,
  .next-btn.next-btn-normal:visited {
    color: #006eff;
  }
  .next-upload-list {
    display: none;
    width: 0;
  }
}
.Declaration__check-base-tips,
.Declaration__check-risk-tips {
  border-radius: 3px;
  padding: 5px 10px;
  .next-icon {
    margin-right: 3px;
  }
}
.Declaration__check-base-tips {
  background: rgba(255, 38, 38, 0.1);
  color: #ff2626;
}
.Declaration__check-risk-tips {
  background: rgba(255, 126, 38, 0.1);
  color: #ff7e26;
}
.Declaration__check-base-content::before,
.Declaration__check-risk-content:before {
  content: " ";
  width: 6px;
  height: 6px;
  border-radius: 50%;
  display: inline-block;
  margin-left: 5px;
  margin-right: 3px;
}
.Declaration__check-risk-content:before {
  background: #ff7e26;
}
.Declaration__check-base-content:before {
  background: #ff2626;
}
.declaration__document-download .lineEllipsis,
.declaration__document-download span {
  color: #5584ff;
  text-decoration: underline;
}
.next-input-group-addon .Declaration__container-goods-btn {
  border-radius: 3px !important;
  .next-btn-helper {
    vertical-align: top;
    line-height: 10px;
  }
}

// 报关申报头部展开收起icon
.next-btn-text.next-medium > .next-btn-icon.next-icon-first.hide_head_icon_up:before {
  transform: rotate(-90deg);
}

.next-btn-text.next-medium > .next-btn-icon.next-icon-first.hide_head_icon_down:before {
  transform: rotate(90deg);
}

.commodity-Drawer {
  .title_block {
    .block_title {
      border-left: 2px solid #3273f4;
      padding-left: 4px;
    }

    .block_place {
      color: #999;
    }
  }

  .next-drawer-body {
    padding-bottom: 60px;
  }

  .drawer_footer {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    border-top: 1px solid #bbbbbb;
    background: #ffffff;
  }
}

.DeclarationTrn__content .single-win-form-container .single-win-item-label {
  width: auto;
}
.commodity_edit_model {
  width: 100%;
  border: 1px solid #bbbbbb;
  height: 280px;
}

// 转关单css
.Transfer_container {
  .single-win-form-container .single-win-item-label {
    width: auto;
  }
}

.Declaration__section_header_title {
  &::before {
    content: "";
    height: 100%;
    border-left: 2px solid #3273f4;
    margin-right: 10px;
  }
}

.single-win-form-container {
  border-top: 1px solid #bbbbbb;
  border-right: 1px solid #bbbbbb;
  font-size: 12px;
  width: 100%;
  table-layout: fixed;
  border-radius: 3px;
  .next-row.next-row-wrap {
    margin: 0 0 -1px -1px !important;
  }
  tr:first-child,
  tr:first-child td:first-child {
    border-top-left-radius: 3px;
  }
  tr:first-child,
  tr:first-child td:last-child {
    border-top-right-radius: 3px;
  }
  tr:last-child,
  tr:last-child td:first-child {
    border-bottom-left-radius: 3px;
  }
  tr:last-child,
  tr:last-child td:last-child {
    border-bottom-right-radius: 3px;
  }
  .next-input {
    border: none;
    width: 100%;
    border-radius: unset;
  }
  .has-error .next-input {
    box-shadow: 0 0 0 1px #f33!important;
  }
  .next-date-picker-trigger[aria-expanded="true"] .next-input,
  .next-focus.next-input {
    box-shadow: 0 0 1px 1px #5f70d6!important;
  }
  .has-error .next-input.next-focus {
    box-shadow: 0 0 1px 1px #f33!important;
  }

  .next-form-item-label,
  .next-form-item-help {
    display: none;
  }
  .next-input-control .next-icon.next-icon-arrow-down,
  .next-input-control .next-icon.next-icon-calendar,
  .next-input-hint.next-input-clear-icon,
  .next-input.next-disabled .next-input-hint-wrap .next-input-hint {
    opacity: 0;
  }
  .next-input.next-focus:not(.next-disabled) .next-input-hint.next-input-clear-icon,
  .next-input:not(.next-disabled) .next-input-hint.next-input-clear-icon:focus,
  .next-input:not(.next-disabled):hover .next-input-hint.next-input-clear-icon,
  .next-input input:hover .next-input-clear {
    opacity: 1;
  }
  .next-form-item-control {
    position: relative;
  }

  .single-win-item-label {
    text-align: right;
    border-left: 1px solid #bbbbbb;
    border-bottom: 1px solid #bbbbbb;
    line-height: 21px;
    padding-right: 3px;
    width: 100px;
    font-size: 12px;
    color: #333;
  }
  .single-win-item {
    height: 21px;
    border-left: 1px solid #bbbbbb;
    border-bottom: 1px solid #bbbbbb;
    >div {
      height: 100%;
    }
    .next-input-group-addon {
      border-left: 1px solid #bbbbbb;
    }
    .next-input-group>.next-input:first-child {
      border-radius: unset !important;
    }
  }
  .next-select-inner,
  .next-select-trigger {
    min-width: unset;
  }
  //.next-input.next-medium .next-input-text-field {
  //  padding: 0;
  //}
  .single-win-item-required input {
    background-color: #FAFFBE;
  }
  .single-win-item-disabled {
    background-color: #E6E6E6!important
  }
  .next-input.next-disabled {
    background-color: #f5f5f5!important
  }
  .next-form-item {
    margin-bottom: 0 !important;
  }
  .next-input.next-medium,
  .next-input.next-medium input {
    height: 20px;
    font-size: 12px;
  }
  .next-input.next-medium .next-input-text-field {
    line-height: 20px;
    padding-left: 3px;
    padding-right: 1px;
  }
  .next-input.next-medium input {
    padding-right: 1px;
  }
  .single-win-error-tips {
    left: 1px;
    top: 29px;
    z-index: 2;
    position: absolute;
    box-shadow: 0 0 0 1px #f33;
    background: #fff;
    padding: 10px;
    border-color: transparent;
    min-width: 120px;
    &:before {
      border: 1px solid #f33;
      position: absolute;
      content: " ";
      width: 10px;
      height: 10px;
      border-left: 0!important;
      border-bottom: 0!important;
      -webkit-transform: rotate(-45deg);
      -ms-transform: rotate(-45deg);
      transform: rotate(-45deg);
      top: -6px;
      background: #fff;
    }
  }
  .next-input.next-medium .next-input-control {
    padding-right: 0;
  }
  //.next-date-picker-input .next-input-control {
  //  display: none;
  //}
  .next-select-trigger.next-has-clear .next-select-inner:hover .next-select-clear,
  .next-has-clear .next-select-values.next-input-text-field:hover +.next-input-control .next-select-clear {
    display: inline-block;
  }

  .el-select {
    width: 100%;
    .el-input__suffix {
      display: none;
    }
  }

  .el-input {
    width: 100%!important;
    .el-input__prefix {
      display: none!important;
    }
    border-radius: 0!important;
  }


  .is-disabled {
    input {
      cursor: default!important;
      background-color: #E6E6E6!important;
    }
  }

}

.mb-8 {
  margin-bottom: 8px;
}

.mr-8 {
  margin-right: 8px;
}
.px-4 {
  padding: 0 4px;
}

.align-items-center {
  align-items: center !important;
}

.justify-content-between {
  justify-content: space-between !important;
}

.d-flex {
  display: flex!important;
}

.full-width {
  width: 100% !important;
}

.flex-column {
  flex-direction: column !important;

}

.lineEllipsis {
  overflow: hidden;
  white-space: nowrap;
  text-overflow:ellipsis;
}

.single-win-form-option {
  .el-select-dropdown__item {
    height: 25px;
    line-height: 25px;
    font-size: 12px;
  }
}

.single-win-button {
  padding: 2px 25px!important;
}

.text-center {
  text-align: center;
}

.single-win-input-button {
  cursor: pointer;
}

.Declaration__section-table_main_body {
  .el-table__empty-block {
    position: sticky;
    left: 0;
    overflow: hidden;
    width: 845px!important;
  }
}

.single-win_dialog_body {
  font-size: 12px;
  .el-form-item__label {
    font-size: 12px;
  }
  .el-table__row {
    font-size: 12px;
  }
}

.single-win-dialog {
  .el-dialog__body {
    padding: 0 30px!important;
  }
  .el-table__empty-text {
    font-size: 12px;
  }
}

.single-win-tips {
  margin-left: 5px;
  position: relative;
  top: -1px;
  height: 20px;
  line-height: 20px;
  font-size: 12px;
  color: red;
}

.single-win-body-table {
  font-size: 12px;
  .el-table__body-wrapper {
    min-height: 80px;
    max-height: 80px;
    overflow-y: scroll;
    .el-table__row {
      td {
        border-bottom: 1px solid #EBEEF5;
      }
    }
  }
}

.single-win-body-main-table {
  font-size: 12px;
  .el-table__body-wrapper {
    min-height: 105px;
    max-height: 105px;
    overflow-y: scroll;
    .el-table__row {
      td {
        border-bottom: 1px solid #EBEEF5;
      }
    }
  }
}

.required-demo {
  display: inline-block;
  background-color: #FAFFBE;
  width: 16px;
  height: 10px;
  border: solid 1px #bbb;
}
.normal-demo {
  display: inline-block;
  background-color: #fff;
  width: 16px;
  height: 10px;
  border: solid 1px #bbb;
}
.disabled-demo {
  display: inline-block;
  background-color: #e6e6e6;
  width: 16px;
  height: 10px;
  border: solid 1px #bbb;
}

.single-win-span {
  margin-left: 5px;
  color: #606266;
  font-size: 12px;
}
