<template>
  <el-dialog :title="title" custom-class="tipsDialog" append-to-body :visible.sync="dialogVisible" width="30%" :before-close="handleClose">
    <slot></slot>
    <div class="content"><i class="el-icon-circle-check" />{{ content }}</div>
    <span slot="footer" class="dialog-footer">
      <el-button size="small" v-if="btnText.cancelText" @click="dialogVisible = false;handleGoOn()">{{ btnText.cancelText || 取消 }}</el-button>
      <el-button size="small" type="primary" @click="handleApply">{{ btnText.conformText || 确定 }}</el-button>
    </span>
  </el-dialog>
</template>
<script>
export default {
  props: {
    btnText: {
      cancelText: '',
      conformText: ''
    },
    title: {
      type: String,
      default: ''
    },
    content: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      dialogVisible: false
    }
  },
  methods: {
    handleClose(done) {
      done()
      //   this.$confirm('确认关闭？')
      //     .then((_) => {
      //       done()
      //     })
      //     .catch((_) => {})
    },
    handleApply() {
      this.dialogVisible = false
      this.$emit('handleApply')
    },
    handleGoOn() {
      this.dialogVisible = false
      this.$emit('handleGoOn')
    }
  }
}
</script>
