<template>
  <div style="display:none"></div>
</template>

<script>
export default {
  name: 'WindowResizeListener',
  data() {
    return {
      width: window.innerWidth,
      height: window.innerHeight
    }
  },
  mounted() {
    this.handleResize() // 初始化时触发一次
    window.addEventListener('resize', this.handleResize)
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.handleResize)
  },
  methods: {
    handleResize() {
      this.width = window.innerWidth
      this.height = window.innerHeight
      this.$emit('resize', { width: this.width, height: this.height })
    }
  }
}
</script>
