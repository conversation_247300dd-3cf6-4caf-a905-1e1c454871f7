@import './variables.scss';
@import './mixin.scss';
@import './transition.scss';
@import './element-ui.scss';
@import './sidebar.scss';
@import './btn.scss';
@import './declaration.scss';
@import './form-page.scss';
@import './richText.scss';

body {
  height: 100%;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  //font-family: SourceHanSansCN-Medium, SourceHanSansCN, Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB,
  //  Microsoft YaHei, Arial, sans-serif;
}

label {
  font-weight: 700;
}

html {
  height: 100%;
  box-sizing: border-box;
}

#app {
  height: 100%;
}

*,
*:before,
*:after {
  box-sizing: inherit;
}

.no-padding {
  padding: 0px !important;
}

.padding-content {
  padding: 4px 0;
}

a:focus,
a:active {
  outline: none;
}

a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}

div:focus {
  outline: none;
}

.fr {
  float: right;
}

.fl {
  float: left;
}

.pr-5 {
  padding-right: 5px;
}

.pl-5 {
  padding-left: 5px;
}

.block {
  display: block;
}

.pointer {
  cursor: pointer;
}

.inlineBlock {
  display: block;
}

.clearfix {
  &:after {
    visibility: hidden;
    display: block;
    font-size: 0;
    content: ' ';
    clear: both;
    height: 0;
  }
}

aside {
  background: #eef1f6;
  padding: 8px 24px;
  margin-bottom: 20px;
  border-radius: 2px;
  display: block;
  line-height: 32px;
  font-size: 16px;
  font-family: SourceHanSansCN-Regular, SourceHanSansCN, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen,
    Ubuntu, Cantarell, 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  color: #2c3e50;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  a {
    color: #337ab7;
    cursor: pointer;

    &:hover {
      color: rgb(32, 160, 255);
    }
  }
}

//main-container全局样式
.app-container {
  padding: 20px;
}

.components-container {
  margin: 30px 50px;
  position: relative;
}

.pagination-container {
  margin-top: 30px;
}

.text-center {
  text-align: center;
}

.sub-navbar {
  height: 50px;
  line-height: 50px;
  position: relative;
  width: 100%;
  text-align: right;
  padding-right: 20px;
  transition: 600ms ease position;
  background: linear-gradient(
    90deg,
    rgba(32, 182, 249, 1) 0%,
    rgba(32, 182, 249, 1) 0%,
    rgba(33, 120, 241, 1) 100%,
    rgba(33, 120, 241, 1) 100%
  );

  .subtitle {
    font-size: 20px;
    color: #fff;
  }

  &.draft {
    background: #d0d0d0;
  }

  &.deleted {
    background: #d0d0d0;
  }
}

.link-type,
.link-type:focus {
  color: #3366FF;
  cursor: pointer;

  &:hover {
    color: #3366FF;
  }
}

.filter-container {
  padding-bottom: 10px;

  .filter-item {
    display: inline-block;
    vertical-align: middle;
    margin-bottom: 10px;
  }
}

//refine vue-multiselect plugin
.multiselect {
  line-height: 16px;
}

.multiselect--active {
  z-index: 1000 !important;
}

@font-face {
  font-family: SourceHanSansCN-Medium;
  src: url('~@/assets/custom-theme/fonts/SourceHanSansCN-Medium.otf');
}

@font-face {
  font-family: SourceHanSansCN-Regular;
  src: url('~@/assets/custom-theme/fonts/SourceHanSansCN-Regular.otf');
}

.mb-15 {
  margin-bottom: 15px;
}

.mt-8 {
  margin-top: 8px;
}

.ml-10 {
  margin-left: 10px;
}

.ml-24 {
  margin-left: 24px;
}

.mt-15 {
  margin-top: 15px;
}

.bg-white {
  background: white!important;
}

.mt-20 {
  margin-top: 20px;
}
.m-20 {
  margin: 20px;
}

.cursor-pointer {
  cursor: pointer;
}

.bg-white {
  background: #fff;
}

.mt-30 {
  margin-top: 30px;
}

.text-right {
  text-align: right;
}

.p-8 {
  padding: 8px;
}

.pt-4 {
  padding-top: 4px;
}

.ml-0 {
  margin-left: 0;
}

.pt-15 {
  padding-top: 15px;
}
.pt-10 {
  padding-top: 10px!important;
}

.table-tip-icon {
  color: #e6a23c;
}

.mt-14 {
  margin-top: 14px;
}

.mt-6 {
  margin-top: 6px;
}

.pt-10 {
  padding-top: 10px!important;
}

.mb-10 {
  margin-bottom: 10px!important;
}

.w-100 {
  width: 100%;
}

.ml-20 {
  margin-left: 20px;
}

.text-14 {
  font-size: 14px;
}

.mb-20 {
  margin-bottom: 20px;
}

.mt-10 {
  margin-top: 10px!important;
}

.mb-13 {
  margin-bottom: 13px!important;
}

.ml-50 {
  margin-left: 50px;
}

.pb-15 {
  padding-bottom: 15px!important;
}

.p-15 {
  padding: 15px;
}
.pr-24{
  padding-right: 24px;
}

.pl-24{
  padding-left: 24px;
}

.mt-16{
  margin-top: 16px;
}
.mb-16{
  margin-bottom: 16px;
}
.pb-16{
  padding-bottom: 16px;
}
.pt-16{
  padding-top: 16px;
}

.mb-0 {
  margin-bottom: 0!important;
}

.el-button--mini {
  font-size: 14px!important;
}

.inline-autocomplete-popper {
  width: 220px!important;
}

.mt-40 {
  margin-top: 40px;
}

.mt-5 {
  margin-top: 5px!important;
}

.mb-5 {
  margin-bottom: 5px!important;
}

.pb-5 {
  padding-bottom: 5px!important;
}

.pb-8 {
  padding-bottom: 8px!important;
}

.mt-8 {
  margin-top: 8px!important;
}

.mr-10 {
  margin-right: 10px;
}

.d-inline-block {
  display: inline-block;
}

.mt-30 {
  margin-top: 30px!important;
}

.p-20 {
  padding: 20px!important;
}

.m-15 {
  margin: 15px;
}

.mt-10 {
  margin-top: 10px!important;
}

.border-bottom {
  border-bottom: 1px dashed rgba(0,0,0,.1);
}

.border-bottom {
  border-bottom: 1px dashed rgba(0,0,0,.1);
}

.pt-0 {
  padding-top: 0!important;
}

.new-title {
  height: 48px;
  padding: 0 20px;
  margin-bottom: 20px;
  border-bottom: 2px solid #3366ff;
  display: flex;
  align-items: center;
  justify-content: space-between;

  .right {
    display: flex;
    font-size: 14px;
    justify-content: center;
    align-items: center;
    color: #409eff;
    cursor: pointer;

    .text {
      margin-right: 8px;
    }
  }
}

.new-title-wrap {
  display: flex;
  align-items: center;
  justify-content: left;

  img {
    width: 13px;
    height: 16px;
    margin-right: 8px;
  }
}

.el-form-item__label {
  color: rgba(0,0,0,.85)!important;
}

.el-input__inner {
  border: solid 1px #c2c7cc!important;
}

.el-button--primary {
  border-color: #3366ff!important;
  background-color: #3366ff!important;
  color: #fff!important;
}

.el-button--default:hover {
  background: #fff;
  color: #3366ff;
  border-radius: 4px;
  border: 1px solid #3366FF;
}

.link {
  color: #3366ff!important;
}

.operate {
  .el-button--text {
    color: #3366ff!important;
  }
}

.el-checkbox__input.is-checked .el-checkbox__inner, .el-checkbox__input.is-indeterminate .el-checkbox__inner {
  background-color: #3366ff !important;
}

.btns {
  .is-plain:hover {
    color: #3366ff!important;
    border-radius: 4px!important;
    border: 1px solid #3366FF!important;
  }
}

.dialog-footer {
  .el-button--default:hover {
    color: #3366ff;
    background: #fff;
    border-radius: 4px;
    border: 1px solid #3366FF;
  }
}

.el-tabs__item.is-active {
  color: #3366ff!important;
}

.el-tabs__active-bar {
  background-color: #3366ff!important;
}

.el-tabs__item:hover {
  color: #3366ff!important;
}


.pagination {
  .el-pagination {
    .el-pager .number.active,.el-pager .number.active:hover{
      color: #3366ff !important;
      background-color: #fff!important;
      border-color: #3366ff !important;
    }

    .btn-quicknext:hover {
      color: #3366ff !important;
    }
  }
}

.el-select-dropdown__wrap  {
  .selected {
    color: #3366ff!important;
  }
}

.el-upload-dragger .el-upload__text em {
  color: #3366ff!important;
}

.el-upload-dragger:hover {
  border-color: #3366ff!important;
}

.sidebar-container {
  box-shadow: 2px 0 5px rgba(0, 0, 0, 0.1); /* 右侧阴影 */
}

.mr-6 {
  margin-right: 6px;
}


/* 设置当前页面element全局table 选中某行时的背景色*/
.el-table__body tr.current-row > td {
  background-color: #D6E0FF !important;
}

.el-table__body tr.selected-row > td {
  background-color: #D6E0FF !important;
}


.px-20 {
  padding-left: 20px;
  padding-right: 20px;
}

.d-block {
  display: block;
}

.mt-5 {
  margin-top: 5px;
}

.pt-20 {
  padding-top: 20px;
}

.pt-20 {
  padding-top: 20px;
}

.pt-20 {
  padding-top: 20px;
}

.h-32{
  height: 32px;
}

.is-required{
  .el-input__inner, .el-textarea__inner {
    background-color: #f2f7fe !important;
  }
}
.has-value {
  .el-input__inner, .el-textarea__inner {
    background-color: #fff !important;
  }
}
.is-disabled {
  .el-input__inner, .el-textarea__inner {
    background-color: #F5F7FA !important;
  }
}

.custom-table-account.el-table {
  thead {
    color: #000000;
    font-size: 13px;
    th {
      background: #f6f7f9;
      border-bottom: 1px solid #dcdee3;
      border-right: 1px solid #dcdee3;
      text-align: center;
      padding: 8px 0;
      &:nth-last-child(2) {
        border-right: none;
      }
      &:last-child {
        border-right: none;
      }
    }
  }
  tbody {
    color: #303133;
    td {
      text-align: center;
      padding: 8px 0;
    }
  }
  .link {
    color: #288dfd;
    cursor: pointer;
  }
}