const dict = {
  // 是否草稿
  isDraft: {
    enum: {
      draft: 1
    },
    label: {
      1: '暂存'
    }
  },

  // 颜色
  color: {
    enum: {
      sunshine_orange: 'sunshine_orange' // 阳光橙
    },
    color: {
      sunshine_orange: '#fb9a0e'
    }
  },

  // 操作类型
  operateType: {
    enum: {
      add: 'add', // 添加
      view: 'view', // 查看
      edit: 'edit' // 编辑
    }
  },

  // 动作类型
  actionType: {
    enum: {
      // revoke_edit: 'revoke', // 撤回 => 编辑
      // majorChange_edit: 'majorChange_edit', // 重大变更 => 编辑
      staging_edit: 'staging_edit' // 暂存 => 编辑
    }
  },

  // 变更类型
  changeType: {
    // 自产货物
    zchwrdEnum: {
      simple_change_entContactInfo: 'simple_change_entContactInfo', // 简单变更，企业联系信息
      simple_change_productRelatedEnt: 'simple_change_productRelatedEnt' // 简单变更，产品相关企业
    }
  },

  // 附件材料类型 (前端材料定义用于判断使用，后续材料定义都可以在这里定义枚举)
  materialType: {
    enum: {
      business_license: 'business_license', // 营业执照
      declaration_form: 'declaration_form', // 申报表
      commitment_book: 'commitment_book', // 承诺书
      certificate_electronics_1: 'certificate_electronics_1', // 事业单位法人证书电子件
      certificate_electronics_2: 'certificate_electronics_2', // 民办非企业单位登记证书电子件
      place_prove_1: 'place_prove_1', // 自贸港开展业务场所证明
      supporting_material: 'supporting_material', // 佐证材料
      appendix_material: 'appendix_material', // 附表材料
      report: 'report' // 报告
    }
  },

  // 用户证明材料类型 (接口定义)
  certificateType: {
    enum: {
      BUSINESS_LICENSE: 'BUSINESS_LICENSE', // 营业执照
      INSTITUTION_LEGAL_PERSON_CERTIFICATE: 'INSTITUTION_LEGAL_PERSON_CERTIFICATE', // 事业单位法人证件
      PRIVATE_NON_ENTERPRISE_REGISTRATION_CERTIFICATE: 'PRIVATE_NON_ENTERPRISE_REGISTRATION_CERTIFICATE' // 民办非企业单位登记证书
    }
  },

  // 享惠申请类型（申请单分类）
  benefitApplyType: {
    enum: {
      lgszt: 'lgszt',
      zchwrd: 'zchwrd',
      sdqysp: 'sdqysp', // 加工增值免关税-独立申报
      sdqyspn: 'sdqyspn', // 加工增值免关税-独立申报(新)
      sdqysplh: 'sdqysplh',
      hwcjwx: 'hwcjwx'
    },
    label: {
      lgszt: '零关税享惠主体资格',
      zchwrd: '自产货物',
      sdqysp: '加工增值免关税-独立申报', // 旧版
      sdqyspn: '加工增值免关税-独立申报', // 新版，测试环境使用
      sdqysplh: '加工增值免关税-联合申报（累计）',
      hwcjwx: '货物出境维修返岛'
    },
    options: [
      { label: '零关税享惠主体资格', value: 'lgszt' },
      { label: '自产货物', value: 'zchwrd' },
      // { label: '加工增值免关税-独立申报', value: 'sdqyspn' },
      // { label: '加工增值免关税-独立申报', value: 'sdqysp' }, // 旧版
      { label: '加工增值免关税-独立申报', value: 'sdqyspn' }, // 新版，测试环境使用
      { label: '加工增值免关税-联合申报（累计）', value: 'sdqysplh' }
      // { label: '货物出境维修返岛', value: 'hwcjwx' },
    ],
    remark: {
      lgszt: '在海南自由贸易港登记注册并具有独立法人资格的企业，海南自由贸易港内的事业单位，以及海南自由贸易港内由科技部、教育部会同民政部核定的，或由省级科技、教育主管部门会同省级民政部门核定的在海南自由贸易港登记的科技类、教育类民办非企业单位，可按规定申报享惠主体资格。',
      zchwrd: '海南自产货物是指予以认定的在海南自由贸易港生产加工、作为加工增值备案企业采购使用并纳入增值成分的货物。加工增值备案企业采购《企业（货物）名录》内的货物，可以按规定在测算加工增值比例时从境内采购料件价格中扣除海南自产货物价格，纳入增值成分。',
      sdqysp: '海南自由贸易港内鼓励类产业企业生产的含进口料件在海南自由贸易港加工增值达到或超过30%的货物，从海南自由贸易港进入内地免征进口关税，照章征收进口环节增值税和消费税。',
      sdqyspn: '海南自由贸易港内鼓励类产业企业生产的含进口料件在海南自由贸易港加工增值达到或超过30%的货物，从海南自由贸易港进入内地免征进口关税，照章征收进口环节增值税和消费税。',
      hwcjwx: '',
      sdqysplh: '保税进口的货物在海南自由贸易港内经过生产工序上下游不同的备案企业加工制造产生增值的，增值部分可以累计计算。（须由加工增值内销企业进行申报）'
    }
  },

  // 申报类型（单据类型）
  applyType: {
    enum: {
      // 字符串
      applyType_1: '1', // 申报
      applyType_0: '0', // 简单变更
      applyType_2: '2', // 重大变更
      applyType_3: '3', // 取消
      applyType_4: '4', // 注销
      applyType_5: '5', // 申诉
      // 数字
      declare: 1, // 申报
      simpleChange: 0, // 简单变更
      change: 2, // 重大变更
      cancel: 3, // 取消
      cancellation: 4, // 注销
      appeal: 5 // 申诉
    },
    label: {
      1: '申报',
      0: '变更',
      2: '变更',
      3: '取消',
      4: '注销',
      5: '申诉'
    },
    changeOptions: [
      { label: '简单变更', value: 0 },
      { label: '重大变更', value: 2 }
    ],
    options: [
      { label: '申报', value: '1' },
      { label: '变更', value: '2' },
      { label: '取消', value: '3' },
      { label: '注销', value: '4' },
      { label: '申诉', value: '5' }
    ]
  },

  oneToMoreStatus: {
    enum: {
      Auditing: '1',
      Completed: '2'

    },
    label: {
      1: '申报中',
      2: '已完结'
    },
    options: [
      { label: '申报中', value: '1' },
      { label: '已完结', value: '2' }
    ]
  },

  // 企业类型
  entType: {
    enum: {
      entType_1: '1',
      entType_2: '2',
      entType_3: '3'
    },
    label: {
      1: '企业',
      2: '事业单位',
      3: '民办非企业'
    },
    options: []
  },

  // 主体类型
  principalType: {
    enum: {
      hnqy: 'hnqy',
      sydw: 'sydw',
      mbfqy: 'mbfqy',
      mbfqy2: 'mbfqy2'
    },
    label: {
      hnqy: '企业',
      sydw: '事业单位',
      mbfqy: '科技类民办非企业单位',
      mbfqy2: '教育类民办非企业单位'
    },
    options: [
      { label: '企业', value: 'hnqy', disabled: false },
      { label: '事业单位', value: 'sydw', disabled: false },
      { label: '科技类民办非企业单位', value: 'mbfqy', disabled: false },
      { label: '教育类民办非企业单位', value: 'mbfqy2', disabled: false }
    ]
  },

  // 委托状态
  entrustStatus: {
    enum: {
      save: 'save',
      revoked: 'revoked',
      approved: 'approved',
      notApproved: 'notApproved',
      del: 'del'
    },
    label: {
      save: '已发起',
      revoked: '已撤回',
      approved: '已确认',
      notApproved: '已拒绝',
      del: '已解除'
    },
    options: [
      { label: '已发起', value: 'save', disabled: false },
      { label: '已确认', value: 'approved', disabled: false },
      { label: '已拒绝', value: 'notApproved', disabled: false },
      { label: '已解除', value: 'del', disabled: false },
      { label: '已撤回', value: 'revoked', disabled: false }
    ]
  },
  // 委托内容
  entrustContent: {
    enum: {
      zcsl: '1',
      ljcpwh: '2',
      zcqdrz: '3',
      jgzzhs: '4',
      zcqdcz: '5',
      cksq: '6',
      cdhfqd: '7',
      zchwzc: '8',
      jejz: '9',
      zcbh: '10'
    },
    label: {
      zcsl: '账册设立',
      ljcpwh: '料件成品维护',
      zcqdrz: '账册清单（入账）',
      jgzzhs: '加工增值核算及自产货物采购管理',
      zcqdcz: '账册清单（出账）',
      cksq: '出库申请',
      cdhfqd: '出岛核放清单',
      zchwzc: '自产货物账册查询及销售管理',
      jejz: '金二结转', // 自己定义的
      zcbh: '账册报核' // 自己定义的
    },
    options: [
      { label: '账册设立', value: '1', disabled: false },
      { label: '料件成品维护', value: '2', disabled: false },
      { label: '账册清单（入账）', value: '3', disabled: false },
      { label: '加工增值核算及自产货物采购管理', value: '4', disabled: false },
      { label: '账册清单（出账）', value: '5', disabled: false },
      { label: '出库申请', value: '6', disabled: false },
      { label: '出岛核放清单', value: '7', disabled: false },
      { label: '自产货物账册查询及销售管理', value: '8', disabled: false }
    ]
  },

  // 是否（通用）。   后续开关类的都可以在这里扩展
  yesNo: {
    enum: {
      yes: '1',
      no: '0'
    },
    label: {
      1: '是',
      0: '否'
    },
    options: [
      { label: '是', value: '1' },
      { label: '否', value: '0' }
    ]
  },

  vatRate: {
    enum: {
      yes: '1',
      no: '0'
    },
    label: {
      0: '9%',
      1: '12%'
    },
    options: [
      { label: '9%', value: '0' },
      { label: '12%', value: '1' }
    ]
  },

  // 是否从事维修
  isMaintain: {
    enum: {
      yes: '1',
      no: '0'
    },
    label: {
      1: '是',
      0: '否'
    },
    options: [
      { label: '是', value: '1' },
      { label: '否', value: '0' }
    ]
  },

  // 审核状态
  approvalStatus: {
    enum: {
      WAIT_AUDIT: 'WAIT_AUDIT',
      AUDITING: 'AUDITING',
      AUDIT_NOT_APPROVED: 'AUDIT_NOT_APPROVED',
      AUDIT_APPROVED: 'AUDIT_APPROVED',
      AUDIT_RETREAT: 'AUDIT_RETREAT'
    },
    label: {
      WAIT_AUDIT: '待审核',
      AUDITING: '审核中',
      AUDIT_NOT_APPROVED: '审核不通过',
      AUDIT_APPROVED: '审核通过',
      AUDIT_RETREAT: '已撤回'
    },
    color: {
      WAIT_AUDIT: '#FAAD14',
      AUDITING: '#409EFF',
      AUDIT_NOT_APPROVED: '#FF4D4F',
      AUDIT_APPROVED: '#52C41A',
      AUDIT_RETREAT: '#000000',
      'null': '#000000'
    },
    nodeColor: {
      WAIT_AUDIT: '#FAAD14',
      AUDITING: '#409EFF',
      AUDIT_NOT_APPROVED: '#FF4D4F',
      AUDIT_APPROVED: '#52C41A',
      AUDIT_RETREAT: '#BFBFBF',
      'null': '#BFBFBF'
    },
    options: [
      { label: '待审核', value: 'WAIT_AUDIT' },
      { label: '审核中', value: 'AUDITING' },
      { label: '审核不通过', value: 'AUDIT_NOT_APPROVED' },
      { label: '审核通过', value: 'AUDIT_APPROVED' },
      { label: '已撤回', value: 'AUDIT_RETREAT' }
    ]
  },

  // 会商状态
  holdBusiness: {
    enum: {
      noLaunch: 0,
      launch: 1,
      launchFinish: 2
    },
    label: {
      0: '未发起会商', // 未发起
      1: '正在会商', // 已发起
      2: '会商结束' // 发起完成
    },
    btnLabel: {
      0: '发起工作会商',
      1: '正在工作会商',
      2: '结束工作会商'
    },
    processLabel: {
      0: '',
      1: '进入工作会商',
      2: ''
    },
    options: [
      { label: '未发起会商', value: 0 },
      { label: '正在会商', value: 1 },
      { label: '会商结束', value: 2 }
    ]
  },

  // 企业信用
  entCredit: {
    enum: {
    },
    label: {
      '高级认证企业': '高级认证企业',
      '白名单企业': '白名单企业',
      '重点关注企业': '重点关注企业',
      '不良信用企业': '不良信用企业'
    },
    img: {
      '高级认证企业': require('@/assets/images/列表尺寸-高级认证企业.png'),
      '白名单企业': require('@/assets/images/列表尺寸-白名单企业.png'),
      '重点关注企业': require('@/assets/images/列表尺寸-重点关注企业.png'),
      '不良信用企业': require('@/assets/images/失信.png')
    },
    options: [
    ]
  },

  // 所属市县（零关税）
  belongCityCounty: {
    enum: {
    },
    label: {
      460100: '海口市',
      460200: '三亚市',
      460300: '三沙市',
      460400: '儋州市',
      469001: '五指山市',
      469005: '文昌市',
      469002: '琼海市',
      469006: '万宁市',
      469007: '东方市',
      469025: '定安县',
      469026: '屯昌县',
      469027: '澄迈县',
      469028: '临高县',
      469030: '白沙黎族自治县',
      469031: '昌江黎族自治县',
      469033: '乐东黎族自治县',
      469034: '陵水黎族自治县',
      469035: '保亭黎族苗族自治县',
      469036: '琼中黎族苗族自治县'
    },
    options: [
      { label: '海口市', value: '460100' },
      { label: '三亚市', value: '460200' },
      { label: '三沙市', value: '460300' },
      { label: '儋州市', value: '460400' },
      { label: '五指山市', value: '469001' },
      { label: '文昌市', value: '469005' },
      { label: '琼海市', value: '469002' },
      { label: '万宁市', value: '469006' },
      { label: '东方市', value: '469007' },
      { label: '定安县', value: '469025' },
      { label: '屯昌县', value: '469026' },
      { label: '澄迈县', value: '469027' },
      { label: '临高县', value: '469028' },
      { label: '白沙黎族自治县', value: '469030' },
      { label: '昌江黎族自治县', value: '469031' },
      { label: '乐东黎族自治县', value: '469033' },
      { label: '陵水黎族自治县', value: '469034' },
      { label: '保亭黎族苗族自治县', value: '469035' },
      { label: '琼中黎族苗族自治县', value: '469036' }
    ]
  },

  // 所在地海关
  customs: {
    label: {
      6400: '海口关区',
      6401: '海口港海关',
      6402: '三亚海关',
      6403: '八所海关',
      6404: '洋浦海关',
      6405: '海保税区',
      6406: '文昌海关',
      6407: '海口美兰机场海关',
      6408: '洋浦保税港区',
      6409: '马村港海关', // 海口综合保税区园区、马村港海关
      6410: '马村港海关', // 马村港海关
      6411: '椰城海关',
      6412: '三沙海关',
      6413: '博鳌机场海关',
      6414: '海口空港综合保税区',
      6415: '三亚市保税物流中心（B型）',
      6416: '三亚机场海关'
    },
    options: [
      // { label: '海口关区', value: '6400', matchRegionName: [] },
      // { label: '海口港海关', value: '6401', matchRegionName: [] },
      { label: '三亚海关', value: '6402', matchRegionName: ['三亚市', '五指山市', '乐东黎族自治县', '陵水黎族自治县', '保亭黎族苗族自治县', '琼中黎族苗族自治县'] },
      { label: '八所海关', value: '6403', matchRegionName: ['东方市', '白沙黎族自治县', '昌江黎族自治县'] },
      { label: '洋浦海关', value: '6404', matchRegionName: ['儋州市'] },
      // { label: '海保税区', value: '6405', matchRegionName: [] },
      { label: '文昌海关', value: '6406', matchRegionName: ['文昌市', '琼海市', '万宁市'] },
      { label: '海口美兰机场海关', value: '6407', matchRegionName: [] },
      { label: '洋浦保税港区', value: '6408', matchRegionName: [] },
      { label: '马村港海关', value: '6409', matchRegionName: [] },
      // { label: '马村港海关', value: '6410', matchRegionName: [] },
      { label: '椰城海关', value: '6411', matchRegionName: ['海口市', '定安县', '屯昌县', '澄迈县', '临高县'] }
      // { label: '三沙海关', value: '6412', matchRegionName: ['三沙市'] },
      // { label: '博鳌机场海关', value: '6413', matchRegionName: [] },
      // { label: '海口空港综合保税区', value: '6414', matchRegionName: [] },
      // { label: '三亚市保税物流中心（B型）', value: '6415', matchRegionName: [] },
      // { label: '三亚机场海关', value: '6416', matchRegionName: [] }
    ]
  },

  // 预提醒处理状态（是否提交）
  warningIsSubmit: {
    enum: {
      yes: 1,
      no: 0
    },
    label: {
      1: '已处理', // 是
      0: '待处理' // 否
    },
    options: [
      { label: '已处理', value: 1 },
      { label: '待处理', value: 0 }
    ]
  },

  // 是否预提醒
  isWarn: {
    enum: {
      yes: 1,
      no: 0
    },
    label: {
      1: '是',
      0: '否'
    },
    options: [
    ]
  },

  // 是否已重复提交
  isReSubmit: {
    enum: {
      yes: 1,
      no: 0
    },
    label: {
      1: '是',
      0: '否'
    }
  },

  // 资格状态
  companyStatus: {
    enum: {
      yes: '1',
      no: '0'
    },
    label: {
      1: '有效',
      0: '失效'
    },
    badgeLabel: {
      1: '已具备',
      0: '未具备'
    },
    options: [
      { label: '有效', value: '1' },
      { label: '无效', value: '0' }
    ]
  },

  // 自产货物状态
  goodsStatus: {
    enum: {
      effective: 'effective'
    },
    label: {
      effective: '有效'
    },
    options: [
      { label: '有效', value: 'effective' }
    ]
  },

  // 授权状态
  authStatus: {
    strEnum: {
      auth: 'auth',
      stopAuth: 'stopAuth'
    },
    enum: {
      unAuth: '0',
      auth: '1',
      stopAuth: '2'
    },
    label: {
      null: '未授权',
      0: '未授权',
      1: '已授权',
      2: '停止授权'
    },
    color: {
      null: '#f6b264',
      0: '#f6b264',
      1: '#7fbb35',
      2: '#ee969d'
    },
    options: [
      { label: '未授权', value: '0' },
      { label: '已授权', value: '1' },
      { label: '停止授权', value: '2' }
    ]
  },

  // 单据类型
  decType: {
    enum: {
      account: 'account',
      stock: 'stock'
    },
    label: {
      account: '出账清单',
      stock: '出库申请单'
    },
    options: [
      { label: '出账清单', value: 'account' },
      { label: '出库申请单', value: 'stock' }
    ]
  }

}

export default dict
