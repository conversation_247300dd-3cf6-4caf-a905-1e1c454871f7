<template>
  <div v-show="tipVisible">
    <div class="tip-wrap">
      <i class="el-icon-warning"></i>
      系统监测到承运商“{{ infoData.carrierCompanyName }}”已为本单报送物流运输信息，您可使用该数据快速填入承运车辆、运输商品等信息！
      <span class="downPointer" @click="onView">点击查看</span>
    </div>

    <LogisticsTransportInfoDialog
      ref="logisticsRef"
      :visible="logisticsInfoVisible"
      :multiple="multiple"
      @close="handleClose"
      @confirm="handleConfirm"
    ></LogisticsTransportInfoDialog>
  </div>
</template>

<script>
import LogisticsTransportInfoDialog from './LogisticsTransportInfoDialog.vue'

export default {
  components: {
    LogisticsTransportInfoDialog
  },
  props: {
    // 是否多选
    multiple: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      tipVisible: false,
      logisticsInfoVisible: false,
      infoData: {},
      listData: []
    }
  },
  created() {
  },
  methods: {
    handleClose() {
      this.logisticsInfoVisible = false
    },
    handleConfirm(val) {
      this.$emit('confirm', val, this.handleClose)
    },
    onView() {
      this.logisticsInfoVisible = true
    },

    // 请求
    async search(searchParams = {}) {
      const data = await this.$refs.logisticsRef.search(searchParams)
      this.infoData = data || {}
      this.tipVisible = !!data?.transportVehicleVOList?.length
      return {
        hasLogisticsTip: this.tipVisible
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.tip-wrap{
  background: #d9e5f9;
  padding: 4px 10px;
  display: flex;
  align-items: center;
  color: #848586;
  font-size: 13px;
  line-height: 20px;
  .el-icon-warning{
    color: #1990fd;
    padding-right: 4px;
  }
}
</style>
