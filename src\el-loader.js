import Vue from 'vue'
import Cookies from 'js-cookie'
import i18n from './lang' // internationalization
import {
  Switch,
  Alert,
  Autocomplete,
  Badge,
  Breadcrumb,
  BreadcrumbItem,
  Tag,
  Button,
  ButtonGroup,
  Card,
  cascader,
  Checkbox,
  Col,
  Collapse,
  CollapseItem,
  DatePicker,
  Dialog,
  Divider,
  Drawer,
  Dropdown,
  DropdownItem,
  DropdownMenu,
  Form,
  FormItem,
  Icon,
  Input,
  Loading,
  Menu,
  MenuItem,
  MenuItemGroup,
  Message,
  MessageBox,
  Notification,
  Option,
  Pagination,
  Popover,
  Radio,
  RadioGroup,
  Row,
  Scrollbar,
  Select,
  Submenu,
  Table,
  TableColumn,
  TabPane,
  Tabs,
  Tooltip,
  Upload,
  RadioButton,
  Steps,
  Step,
  Progress,
  Timeline,
  TimelineItem,
  Link,
  Image,
  Popconfirm,
  CheckboxGroup,
  InputNumber,
  Container,
  Header,
  Main,
  Footer,
  Aside
} from 'element-ui'

import 'element-ui/lib/theme-chalk/index.css'

Vue.use(Pagination)
Vue.use(Divider)
Vue.use(Dialog)
Vue.use(Dropdown)
Vue.use(DropdownMenu)
Vue.use(DropdownItem)
Vue.use(Menu)
Vue.use(Submenu)
Vue.use(MenuItem)
Vue.use(MenuItemGroup)
Vue.use(Input)
Vue.use(Tag)
Vue.use(Button)
Vue.use(ButtonGroup)
Vue.use(Table)
Vue.use(TableColumn)
Vue.use(Tooltip)
Vue.use(Breadcrumb)
Vue.use(BreadcrumbItem)
Vue.use(Form)
Vue.use(FormItem)
Vue.use(Alert)
Vue.use(Icon)
Vue.use(Row)
Vue.use(Col)
Vue.use(Select)
Vue.use(Option)
Vue.use(DatePicker)
Vue.use(Upload)
Vue.use(RadioGroup)
Vue.use(Radio)
Vue.use(Checkbox)
Vue.use(Autocomplete)
Vue.use(Scrollbar)
Vue.use(Collapse)
Vue.use(CollapseItem)
Vue.use(Popover)
Vue.use(Tabs)
Vue.use(TabPane)
Vue.use(Badge)
Vue.use(Card)
Vue.use(Drawer)
Vue.use(Steps)
Vue.use(Step)
Vue.use(Drawer)
Vue.use(RadioButton)
Vue.use(Progress)
Vue.use(Timeline)
Vue.use(TimelineItem)
Vue.use(Link)
Vue.use(Image)
Vue.use(cascader)
Vue.use(Popconfirm)
Vue.use(Loading.directive)
Vue.use(CheckboxGroup)
Vue.use(InputNumber)
Vue.use(Container)
Vue.use(Header)
Vue.use(Main)
Vue.use(Aside)
Vue.use(Footer)
Vue.use(Switch)
Vue.prototype.$loading = Loading.service
Vue.prototype.$msgbox = MessageBox
Vue.prototype.$alert = MessageBox.alert
Vue.prototype.$confirm = MessageBox.confirm
Vue.prototype.$prompt = MessageBox.prompt
Vue.prototype.$notify = Notification
Vue.prototype.$message = Message
Vue.prototype.$ELEMENT = {
  size: Cookies.get('size') || 'medium', // set element-ui default size
  i18n: (key, value) => i18n.t(key, value)
}
