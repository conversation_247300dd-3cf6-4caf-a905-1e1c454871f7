<template>
  <div :class="{ 'has-logo': showLogo }">
    <logo v-if="showLogo" :collapse="isCollapse" />
    <el-scrollbar wrap-class="scrollbar-wrapper">
      <el-menu
        :default-active="activeMenu"
        :collapse="isCollapse"
        :background-color="variables.menuBg"
        :text-color="variables.menuText"
        :unique-opened="false"
        :active-text-color="variables.menuActiveText"
        :collapse-transition="false"
        mode="vertical"
      >
        <sidebar-item
          v-for="route in permission_routes"
          :key="route.path"
          :level="1"
          :item="route"
          :base-path="route.path"
        />
      </el-menu>
    </el-scrollbar>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import Logo from './Logo'
import SidebarItem from './SidebarItem'
import variables from '@/styles/variables.scss'

export default {
  components: { SidebarItem, Logo },
  computed: {
    ...mapGetters(['permission_routes', 'sidebar']),
    activeMenu() {
      const route = this.$route
      const { meta, path } = route
      // if set path, the sidebar will highlight the path you set
      if (meta.activeMenu) {
        return meta.activeMenu
      }
      return path
    },
    showLogo() {
      return this.$store.state.settings.sidebarLogo
    },
    variables() {
      return variables
    },
    isCollapse() {
      return !this.sidebar.opened
    }
  }
}
</script>

<style lang="scss">


.el-submenu {
  font-weight: 400;
  .svg-icon-g {
    fill: #959595;
  }
  &.is-active {
    font-weight: 900;
    .svg-icon-g {
      fill: #3366ff !important;
    }
  }
}
.el-menu-item {
  font-weight: 400;
  .svg-icon-g {
    fill: #959595;
  }
  &.is-active {
    font-weight: 900;
    .svg-icon-g {
      fill: #3366ff !important;
    }
  }
}
</style>

<style lang="scss" scoped>
.sidebar-container {
  // ... 其他样式保持不变

  // 选中时的图标颜色
  ::v-deep .is-active .svg-icon {
    color: #409eff;
    fill: #409eff;
  }
}
</style>
