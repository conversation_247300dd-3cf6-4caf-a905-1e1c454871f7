import Cookies from 'js-cookie'
import { ROUTE_TYPE } from '@/store/mutation-types'

const state = {
  firstEnter: true,
  routeType: Cookies.get(ROUTE_TYPE)
}

const mutations = {
  SET_FIRST_ENTER: (state, val) => {
    state.firstEnter = val
  },
  SET_ROUTE_TYPE: (state, val) => {
    state.routeType = val
    Cookies.set(ROUTE_TYPE, val)
  }
}

const actions = {
  setFirstEnter({ commit }, payload) {
    commit('SET_FIRST_ENTER', payload)
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
