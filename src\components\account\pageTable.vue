<template>
  <div class="l-table">
    <el-table
      ref="pageTable"
      v-loading="dataLoading"
      :empty-text="emptyText"
      class="no-ellipsis"
      tooltip-effect="light"
      highlight-current-row
      :data="tableColumnData"
      border
      :max-height="maxHeight"
      :height="heightCasted"
      style="width: 100%"
      @current-change="handleCurrentChange"
      @selection-change="selectChange"
      @cell-dblclick="cellDblClick"
      @cell-click="cellClick"
      @row-click="RowClick"
    >
      <el-table-column v-if="checkbox" type="selection" width="50" align="center" />
      <el-table-column v-if="showIndex" type="index" width="50" label="序号" align="center" />
      <template v-for="item in columns">
        <el-table-column
          v-if="!item.hidden"
          :key="item.field"
          :show-overflow-tooltip="item.tooltip === undefined ? true : item.tooltip"
          :prop="item.field"
          :class-name="item.className || ''"
          :label="item.label"
          :align="item.align || 'left'"
          :width="item.width"
          :min-width="item.minWidth || item.min_width"
          :fixed="item.fixed"
          :formatter="item.formatter"
        >
          <template v-if="item.tip" #header>
            <div>
              <span>{{ item.label }}</span>
              <el-tooltip class="item" effect="dark" :content="item.tip" placement="top">
                <i class="el-icon-info" style="cursor: pointer"></i>
              </el-tooltip>
            </div>
          </template>
          <template slot-scope="scope">
            <div v-if="item.type === 'switch'">
              <el-switch
                v-model="scope.row[item.field]"
                size="small"
                :active-value="item.activeValue"
                :inactive-value="item.inactiveValue"
              >
              </el-switch>
            </div>
            <div v-else-if="item.type === 'image'">
              <el-image
                style="width: 60px; height: 60px"
                :src="scope.row[item.field]"
                :preview-src-list="[scope.row[item.field]]"
              >
              </el-image>
            </div>
            <div v-else-if="item.formatter" style="overflow: hidden; white-space: nowrap; text-overflow: ellipsis">
              <template v-if="item.type === 'html'">
                <div v-html="item.formatter(scope.row[scope.column.property], scope.row)"></div>
              </template>
              <template v-else>{{ item.formatter(scope.row[scope.column.property], scope.row) }}</template>
            </div>
            <div v-else style="overflow: hidden; white-space: nowrap; text-overflow: ellipsis">
              {{ scope.row[item.field] }}
            </div>
          </template>
        </el-table-column>
      </template>
      <slot name="event"></slot>
    </el-table>
    <div v-if="needPagination" class="l-pages">
      <!-- 分页 -->
      <el-pagination
        :current-page.sync="pages.pageNo"
        :page-sizes="pageSizes"
        :page-size="pages.pageSize"
        :layout="layout"
        :total="total"
        @size-change="sizeChange"
        @current-change="currentChange"
      />
    </div>
  </div>
</template>

<script>
import { getListApi, deleteBathListApi } from '@/api/account/index'
import { isEmpty } from '@/utils/account/common'
export default {
  props: {
    method: {
      type: String,
      default: 'get'
    },
    showIndex: {
      type: Boolean,
      default: false
    },
    emptyText: {
      type: String,
      default: '暂无数据'
    },
    needPagination: {
      type: Boolean,
      default: true
    },
    state: {
      type: Object,
      default() {
        return {
          rowIndex: -1,
          columnIndex: -1
        }
      }
    },
    pages: {
      type: Object,
      default() {
        return {
          pageNo: 1,
          pageSize: 10
        }
      }
    },
    tableData: {
      require: false,
      type: Array,
      default: () => {
        return []
      }
    },
    layout: {
      type: String,
      default: 'total, sizes, prev, pager, next, jumper'
    },
    columns: {
      type: Array,
      default: () => {
        return []
      }
    },
    height: {
      type: [Number, String],
      default: '200'
      // validator(value) {
      //   return typeof value === 'number' || (!isNaN(value) && isFinite(value))
      // }
    },
    dataTotal: {
      type: Number,
      default: 0
    },
    pageSizes: {
      type: Array,
      default() {
        return [10, 20, 30, 50]
      }
    },
    url: {
      type: String,
      default: ''
    },
    needList: {
      type: Boolean,
      default: true
    },
    searchParams: {
      type: Object,
      default() {
        return {}
      }
    },
    searchParamFn: {
      type: Function,
      default: null
    },
    checkbox: {
      type: Boolean,
      default: true
    }
  },

  data() {
    return {
      total: 0,
      dataLoading: false,
      maxHeight: undefined,
      selections: [],
      rowIndex: -1,
      tableColumnData: [], // 表格数据
      currentRow: null
    }
  },
  computed: {
    heightCasted() {
      let val = ''
      if (this.height === 'auto') {
        val = undefined
      } else {
        val = this.height
      }
      return val
    }
  },
  watch: {
    'state.rowIndex': {
      handler(newCond, oldCond) {
        this.rowIndex = newCond
      },
      immediate: true,
      deep: true
    },
    tableData: {
      handler(newVal, oldVal) {
        if (!isEmpty(newVal)) {
          this.tableColumnData = newVal
          this.total = this.tableColumnData.length
        }
      },
      immediate: true,
      deep: true
    },
    dataTotal: {
      handler(newVal, oldVal) {
        if (newVal) {
          this.total = newVal
        }
      },
      immediate: true,
      deep: true
    }
  },
  mounted() {
    // this.getTableList()
  },
  methods: {
    onChange(item, value, index) {
      this.tableColumnData[index][item.field] = value
    },
    handleDelAll() {
      if (this.selections.length === 0) {
        this.$message.warning('请先选择需要删除的数据')
        return
      }
      this.$confirm('是否确定删除该数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const blIds = this.selections.map((selection) => selection.blId)
        deleteBathListApi(this.url, { blIds }).then((res) => {
          if (res.code === 200) {
            this.$message.success('删除成功')
            this.getTableList()
            this.$emit('handleDelSuccess')
          } else {
            this.$message.error(res.msg)
          }
        })
      })
    },
    // getTableList() {
    //   this.dataLoading = true
    //   getListApi(this.url + '/list', this.searchParams).then((res) => {
    //     this.tableColumnData = res.data.list
    //     this.dataLoading = false
    //   })
    // },
    getTableList(method) {
      return new Promise((resolve, reject) => {
        this.dataLoading = true
        const url = this.needList ? this.url + '/list' : this.url
        let params = {}
        if (this.searchParamFn) {
          params = Object.assign(this.searchParamFn(), this.pages)
        } else {
          params = Object.assign(this.searchParams, this.pages)
        }
        getListApi(url, params, this.method)
          .then((res) => {
            this.tableColumnData = res.data?.list || res.data?.records || res.data || []
            this.total = res.data.total
            this.dataLoading = false
            resolve(res.data.list) // 异步请求成功，解析Promise
          })
          .catch((error) => {
            this.dataLoading = false
            reject(error) // 异步请求失败，拒绝Promise
          })
      })
    },
    pageTableClear() {
      this.$refs.pageTable.clearSelection()
    },
    handleCurrentChange(val) {
      this.currentRow = val
    },
    cellDblClick(row, column, cell, event) {
      // 触发自定义事件 cellDblClick，并传递 row, column, cell, event 作为参数
      this.$emit('cellDblClick', row, column, cell, event)
    },
    RowClick(row, column, event) {
      if (this.checkbox) {
        // 切换当前行的选中状态
        this.$refs.pageTable.toggleRowSelection(row)
      }

      this.$emit('RowClick', { row, column, event })
    },
    cellClick(row, column, cell, event) {
      this.$refs.pageTable.setCurrentRow(row)
      this.$emit('cellClick', { row, column, cell, event })
    },
    inputBlur(scoped) {
      this.rowIndex = 0
      this.columnIndex = 0
    },
    selectChange(selection) {
      this.selections = selection
      this.$emit('selectChange', selection)
    },
    fieldChange(row, option) {
      if (option[row]) {
        return option[row]
      }
    },
    sizeChange(item) {
      this.pages.pageSize = item
      this.getTableList()
      // 设置最大高度，滚动可以在可视区看到表头
      this.maxHeight = `${window.innerHeight - 300}px`
      // this.emit('getDataList')
    },
    currentChange(item) {
      this.pages.pageNo = item
      this.getTableList()
      // this.emit('getDataList')
    },

    editInputBlur() {}
  }
}
</script>

<style scoped lang="scss">
.l-table {
  // height: calc(100vh - 420px);
  padding-bottom: 10px;
  .el-table {
    // height: calc(100% - 42px) !important;
  }

  .l-pages {
    margin-top: 10px;
  }
}
:deep().no-ellipsis .cell {
  white-space: normal !important;
  overflow: visible;
  text-overflow: inherit;
}
</style>
