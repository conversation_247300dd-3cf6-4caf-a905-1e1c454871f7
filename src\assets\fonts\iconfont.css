@font-face {
  font-family: "iconfont"; /* Project id 4816072 */
  src: url('iconfont.woff2?t=1739330997219') format('woff2'),
       url('iconfont.woff?t=1739330997219') format('woff'),
       url('iconfont.ttf?t=1739330997219') format('truetype');
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-bianzu1:before {
  content: "\e632";
}

.icon-avatar:before {
  content: "\e619";
}

.icon-a:before {
  content: "\e631";
}

.icon-bianzu:before {
  content: "\e628";
}

.icon-a-iconhome:before {
  content: "\e629";
}

.icon-a-bianzu7:before {
  content: "\e62a";
}

.icon-a-bianzu1:before {
  content: "\e62b";
}

.icon-a-bianzu6:before {
  content: "\e62c";
}

.icon-a-bianzu2:before {
  content: "\e62d";
}

.icon-a-bianzu5:before {
  content: "\e62e";
}

.icon-a-bianzu3:before {
  content: "\e62f";
}

.icon-a-bianzu4:before {
  content: "\e630";
}

.icon-service-record:before {
  content: "\e61c";
}

.icon-first-pass:before {
  content: "\e61d";
}

.icon-size:before {
  content: "\e61e";
}

.icon-receipt-entry:before {
  content: "\e61f";
}

.icon-dashboard:before {
  content: "\e620";
}

.icon-enterprise-infomation:before {
  content: "\e621";
}

.icon-a8:before {
  content: "\e622";
}

.icon-a5:before {
  content: "\e623";
}

.icon-icon:before {
  content: "\e624";
}

.icon-logout:before {
  content: "\e625";
}

.icon-menu:before {
  content: "\e626";
}

.icon-password:before {
  content: "\e627";
}

.icon-lock:before {
  content: "\e60e";
}

.icon-region-enterprises:before {
  content: "\e60f";
}

.icon-regional-customs:before {
  content: "\e610";
}

.icon-change:before {
  content: "\e611";
}

.icon-vehicle-record:before {
  content: "\e612";
}

.icon-a9:before {
  content: "\e613";
}

.icon-home:before {
  content: "\e614";
}

.icon-a4:before {
  content: "\e615";
}

.icon-one:before {
  content: "\e616";
}

.icon-fullscreen:before {
  content: "\e617";
}

.icon-receipt-management:before {
  content: "\e618";
}

.icon-regional-release-record:before {
  content: "\e61a";
}

.icon-a2:before {
  content: "\e61b";
}

.icon-a3:before {
  content: "\e607";
}

.icon-user:before {
  content: "\e608";
}

.icon-void:before {
  content: "\e609";
}

.icon-search:before {
  content: "\e60a";
}

.icon-submit:before {
  content: "\e60b";
}

.icon-alter:before {
  content: "\e60c";
}

.icon-three:before {
  content: "\e60d";
}

.icon-second-pass:before {
  content: "\e602";
}

.icon-two:before {
  content: "\e603";
}

.icon-a7:before {
  content: "\e604";
}

.icon-language:before {
  content: "\e601";
}

.icon-product-record:before {
  content: "\e605";
}

.icon-customs-service-record-management:before {
  content: "\e606";
}

