<template>
  <el-tag
    class="tage-wrap"
    :class="[type]"
  >
    <slot></slot>
  </el-tag>
</template>

<script>
const typeEnum = {
  warning: 'warning',
  danger: 'danger'
}

export default {
  props: {
    // 类型
    type: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      typeEnum
    }
  }
}
</script>

<style lang="scss" scoped>
.tage-wrap{
    background: #3366ff1a;
    border-radius: 4px;
    border: 1px solid #3366ff99;
    line-height: 20px;
    font-weight: 400;
    font-size: 12px;
    color: #3366FF;
    padding: 1px 8px;
    height: auto;

  &.warning{
    background: #FFFBE6;
    border: 1px solid #FCCF71;
    color: #FB9A0E;
  }
  &.danger{
    background: #FFF1F0;
    border: 1px solid #FFCCC7;
    color: #FF4D4F;
  }
}
</style>
