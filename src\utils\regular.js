// 有效整数或小数
export const validIntegerOrDecimalsReg = new RegExp('^(\\-|\\+)?\\d+(\\.\\d+)?$')

// 有效数字
export const validNumberReg = new RegExp('^[0-9]*$')

// 身份证号码
export const validIdCardReg = new RegExp('(^[1-9]\\d{5}(18|19|([23]\\d))\\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$)|(^[1-9]\\d{5}\\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\\d{3}$)')

// 手机号码
export const validPhoneReg = new RegExp('^1[0123456789]\\d{9}$')
