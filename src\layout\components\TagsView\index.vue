<template>
  <div id="tags-view-container" class="tags-view-container clearfix">
    <hamburger
      id="hamburger-container"
      :is-active="sidebar.opened"
      class="hamburger-container"
      @toggleClick="toggleSideBar"
    />
    <span class="tags-view-scroll-btn fl" @click="scrollLeft">
      <i class="icon-triangle-left"></i>
      <i class="icon-triangle-left"></i>
    </span>
    <scroll-pane ref="scrollPane" class="tags-view-wrapper fl" @scroll="handleScroll">
      <router-link
        v-for="tag in visitedViews"
        ref="tag"
        :key="tag.path"
        :class="isActive(tag) ? 'active' : ''"
        :to="{ path: tag.path, query: tag.query, fullPath: tag.fullPath }"
        tag="span"
        class="tags-view-item"
        @click.middle.native="!isAffix(tag) ? closeSelectedTag(tag) : ''"
        @contextmenu.prevent.native="openMenu(tag, $event)"
      >
        <span class="tags-view-title">{{ generateTitle(tag.title) }}</span>
        <span v-if="!isAffix(tag)" class="el-icon-close" @click.prevent.stop="closeSelectedTag(tag)" />
      </router-link>
    </scroll-pane>
    <el-dropdown class="fr" trigger="click" @command="selectDropMenu">
      <span class="tags-view-close-operation">
        {{ $t('tagsView.closeOperation') }}
        <i class="icon-triangle-down"></i>
      </span>
      <el-dropdown-menu slot="dropdown">
        <el-dropdown-item command="all" class="tags-view-close-operation-item">{{
          $t('tagsView.closeAll')
        }}</el-dropdown-item>
        <el-dropdown-item command="other" class="tags-view-close-operation-item">{{
          $t('tagsView.closeOthers')
        }}</el-dropdown-item>
      </el-dropdown-menu>
    </el-dropdown>
    <span class="tags-view-scroll-btn btn-scroll-right fr" @click="scrollRight">
      <i class="icon-triangle-right"></i>
      <i class="icon-triangle-right"></i>
    </span>
    <ul v-show="visible" :style="{ left: left + 'px', top: top + 'px' }" class="contextmenu">
      <li @click="openNewTag(selectedTag)">{{ $t('tagsView.newTag') }}</li>
      <li @click="refreshSelectedTag(selectedTag)">{{ $t('tagsView.refresh') }}</li>
      <li v-if="!isAffix(selectedTag)" @click="closeSelectedTag(selectedTag)">{{ $t('tagsView.close') }}</li>
      <li @click="closeOthersTags">{{ $t('tagsView.closeOthers') }}</li>
      <li @click="closeAllTags(selectedTag)">{{ $t('tagsView.closeAll') }}</li>
    </ul>
  </div>
</template>

<script>
import ScrollPane from './ScrollPane'
import { generateTitle } from '@/utils/i18n'
import Hamburger from '@/components/Hamburger'
import { mapGetters } from 'vuex'
import waves from '@/directive/waves/index.js' // 水波纹指令
import path from 'path'

export default {
  components: { ScrollPane, Hamburger },
  directives: {
    waves
  },
  data() {
    return {
      visible: false,
      top: 0,
      left: 0,
      selectedTag: {},
      affixTags: []
    }
  },
  computed: {
    ...mapGetters(['sidebar']),
    visitedViews() {
      return this.$store.state.tagsView.visitedViews
    },
    routes() {
      return this.$store.state.permission.routes
    }
  },
  watch: {
    $route() {
      this.addTags()
      this.moveToCurrentTag()
    },
    visible(value) {
      if (value) {
        document.body.addEventListener('click', this.closeMenu)
      } else {
        document.body.removeEventListener('click', this.closeMenu)
      }
    }
  },
  mounted() {
    this.initTags()
    this.addTags()
  },
  methods: {
    generateTitle, // generateTitle by vue-i18n
    toggleSideBar() {
      this.$store.dispatch('app/toggleSideBar')
    },
    isActive(route) {
      return route.path === this.$route.path
    },
    isAffix(tag) {
      return tag.meta && tag.meta.affix
    },
    filterAffixTags(routes, basePath = '/') {
      let tags = []
      routes.forEach(route => {
        if (route.meta && route.meta.affix) {
          const tagPath = path.resolve(basePath, route.path)
          tags.push({
            fullPath: tagPath,
            path: tagPath,
            name: route.name,
            meta: { ...route.meta }
          })
        }
        if (route.children) {
          const tempTags = this.filterAffixTags(route.children, route.path)
          if (tempTags.length >= 1) {
            tags = [...tags, ...tempTags]
          }
        }
      })
      return tags
    },
    initTags() {
      const affixTags = (this.affixTags = this.filterAffixTags(this.routes))
      for (const tag of affixTags) {
        // Must have tag name
        if (tag.name) {
          this.$store.dispatch('tagsView/addVisitedView', tag)
        }
      }
    },
    addTags() {
      const { name } = this.$route
      if (name) {
        this.$store.dispatch('tagsView/addView', this.$route)
      }
      return false
    },
    moveToCurrentTag() {
      const tags = this.$refs.tag
      if (!tags) {
        return
      }
      this.$nextTick(() => {
        for (const tag of tags) {
          if (tag.to.path === this.$route.path) {
            this.$refs.scrollPane.moveToTarget(tag)
            // when query is different then update
            if (tag.to.fullPath !== this.$route.fullPath) {
              this.$store.dispatch('tagsView/updateVisitedView', this.$route)
            }
            break
          }
        }
      })
    },
    refreshSelectedTag(view) {
      this.$store.dispatch('tagsView/delCachedView', view).then(() => {
        const { fullPath } = view
        this.$nextTick(() => {
          this.$router.replace({
            path: '/redirect' + fullPath
          })
        })
      })
    },
    closeSelectedTag(view) {
      this.$store.dispatch('tagsView/delView', view).then(({ visitedViews }) => {
        if (this.visitedViews.length < 1) {
          this.$router.replace({ path: '/' })
          return
        }
        if (this.isActive(view)) {
          this.toLastView(visitedViews, view)
        }
      })
    },
    closeOthersTags() {
      this.$router.push(this.selectedTag)
      this.$store.dispatch('tagsView/delOthersViews', this.selectedTag).then(() => {
        this.moveToCurrentTag()
      })
    },
    closeAllTags(view) {
      this.$store.dispatch('tagsView/delAllViews').then(({ visitedViews }) => {
        if (this.affixTags.some(tag => tag.path === view.path)) {
          return
        }
        // this.toLastView(visitedViews, view)
        this.$router.replace({ path: '/' })
      })
    },
    toLastView(visitedViews, view) {
      const latestView = visitedViews.slice(-1)[0]
      if (latestView) {
        this.$router.push(latestView.fullPath)
      } else {
        // now the default is to redirect to the home page if there is no tags-view,
        // you can adjust it according to your needs.
        // to reload home page
        this.$router.replace({ path: '/redirect' + view.fullPath, query: { ...view.query }})
      }
    },
    openMenu(tag, e) {
      const menuMinWidth = 105
      const offsetLeft = this.$el.getBoundingClientRect().left // container margin left
      const offsetWidth = this.$el.offsetWidth // container width
      const maxLeft = offsetWidth - menuMinWidth // left boundary
      const left = e.clientX - offsetLeft + 15 // 15: margin right

      if (left > maxLeft) {
        this.left = maxLeft
      } else {
        this.left = left
      }

      this.top = e.clientY
      this.visible = true
      this.selectedTag = tag
    },
    closeMenu() {
      this.visible = false
    },
    handleScroll() {
      this.closeMenu()
    },
    scrollLeft() {
      const tags = this.$refs.tag
      for (let i = 0; i < tags.length; i++) {
        if (tags[i].to.path === this.$route.path && i > 0) {
          this.$router.push({ path: tags[i - 1].to.path })
          break
        }
      }
      // this.$refs.scrollPane.scrollLeft()
    },
    scrollRight() {
      // this.$refs.scrollPane.scrollRight()
      const tags = this.$refs.tag
      for (let i = 0; i < tags.length; i++) {
        if (tags[i].to.path === this.$route.path && i < tags.length - 1) {
          this.$router.push({ path: tags[i + 1].to.path })
          break
        }
      }
    },
    selectDropMenu(command) {
      const views = this.visitedViews
      for (let i = 0; i < views.length; i++) {
        if (views[i].path === this.$route.path) {
          this.selectedTag = views[i]
          break
        }
      }
      if (command === 'all') {
        this.closeAllTags(this.selectedTag)
      } else if (command === 'other') {
        this.closeOthersTags()
      }
    },
    openNewTag(tag) {
      const path = window.location.href.replace(/\/#\/(\w|\W)+/, `/#${tag.fullPath}`)
      window.open(path, '_blank')
    }
  }
}
</script>

<style lang="scss" scoped>
@import '~@/styles/mixin.scss';
.tags-view-container {
  width: 100%;
  height: 40px;
  background: #F5F5F5;
  box-shadow: 0px 0px 8px 0px rgba(0,0,0,0.15);
  .hamburger-container {
    line-height: 40px;
    height: 40px;
    float: left;
    background-color: #fff;
    cursor: pointer;
    transition: background 0.3s;
    -webkit-tap-highlight-color: transparent;
    // &:hover {
    //   background: rgba(0, 0, 0, 0.025);
    // }
  }
  .tags-view-scroll-btn {
    display: inline-block;
    width: 40px;
    height: 40px;
    line-height: 38px;
    text-align: center;
    cursor: pointer;
    border: solid #ddd;
    border-width: 0 1px;
    background-color: #fff;
    .icon-triangle-left {
      display: inline-block;
      @include triangle(8px, 5px, #a0a9b5, left);
      pointer-events: none;
    }
    .icon-triangle-right {
      display: inline-block;
      @include triangle(8px, 5px, #a0a9b5, right);
      pointer-events: none;
    }
  }
  .tags-view-close-operation {
    display: inline-block;
    width: 100px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    background-color: #fff;
    font-size: 12px;
    color: #909399;
    cursor: pointer;
    .icon-triangle-down {
      display: inline-block;
      @include triangle(8px, 5px, #a0a9b5, down);
      vertical-align: middle;
      pointer-events: none;
    }
  }
  .tags-view-wrapper {
    width: calc(100% - 220px);
    .tags-view-item {
      display: inline-block;
      position: relative;
      cursor: pointer;
      height: 40px;
      line-height: 40px;
      color: #606266;
      background: #F5F5F5;
      box-shadow: 0px 0px 8px 0px rgba(0,0,0,0.15);
      padding: 0 15px;
      font-size: 12px;
      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 10px;
        display: inline-block;
        height: 20px;
        border-right: 1px solid #ddd;
      }
      &:first-child {
        &::before {
          display: none;
        }
      }
      &.active {
        background-color: #fff;
        color: #303133;
        & + .tags-view-item {
          &::before {
            display: none;
          }
        }
        &::before {
          display: none;
        }
      }
      .tags-view-title {
        font-size: 14px;
        padding-right: 10px;
        pointer-events: none;
      }
    }
  }
  .contextmenu {
    margin: 0;
    background: #fff;
    z-index: 3000;
    position: absolute;
    list-style-type: none;
    padding: 5px 0;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 400;
    color: #333;
    box-shadow: 2px 2px 3px 0 rgba(0, 0, 0, 0.3);
    li {
      margin: 0;
      padding: 7px 16px;
      cursor: pointer;
      &:hover {
        background: #eee;
      }
    }
  }
}
</style>

<style lang="scss">
.tags-view-close-operation-item {
  font-size: 12px !important;
}
//reset element css of el-icon-close
.tags-view-wrapper {
  .tags-view-item {
    .el-icon-close {
      width: 12px;
      height: 12px;
      border-radius: 100%;
      text-align: center;
      // transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
      // transform-origin: 100% 50%;
      color: #fff;
      background-color: #c0c4cc;
      &:before {
        transform: scale(0.7);
        display: inline-block;
        vertical-align: 0px;
        font-weight: bold;
      }
      // &:hover {
      //   background-color: #b4bccc;
      //   color: #fff;
      // }
    }
  }
}
</style>
