<template>
  <el-dialog
    :title="title"
    :visible.sync="visible"
    width="1200px"
    modal-append-to-body
    append-to-body
    @close="handleClose"
  >

    <el-form class="form-wrap" :model="filterForm" :inline="true" size="small" label-positio="right">
      <el-row>
        <el-col :span="24">
          <el-form-item label="关联单证编号：">
            {{ infoData.decNo }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="承运商：">
            {{ infoData.carrierCompanyName }}
          </el-form-item>
        </el-col>
        <el-col class="valid-col" :span="12">
          <el-form-item label="">
            <el-checkbox v-model="showValid" @change="validChange">仅显示有效运输单</el-checkbox>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <el-table
      ref="tableRef"
      class="custom-table-account full-width"
      border
      max-height="460px"
      :data="listData"
      :row-class-name="rowClassName"
      @row-click="handleRowClick"
    >
      <el-table-column align="center" width="50px" fixed="left">
        <template slot-scope="scope">
          <span>
            <el-checkbox v-model="scope.row.checked" :disabled="scope.row.isRelated === '1' || scope.row.status !== statusEnum.status_2" @click.native.stop @change="checkboxChange(scope.row)"></el-checkbox>
          </span>
        </template>
      </el-table-column>
      <el-table-column label="运输报送单号" align="center" show-overflow-tooltip min-width="130px" fixed="left">
        <template slot-scope="scope">
          <span>{{ scope.row.transDeclareNo }}</span>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" show-overflow-tooltip min-width="150px" fixed="left">
        <template slot-scope="scope">
          <span>{{ statusMap[scope.row.status] || scope.row.status }}</span>
        </template>
      </el-table-column>
      <el-table-column label="车牌号" align="center" show-overflow-tooltip min-width="120px">
        <template slot-scope="scope">
          <span>{{ scope.row.carNo }}</span>
        </template>
      </el-table-column>
      <el-table-column label="司机" align="center" show-overflow-tooltip min-width="120px">
        <template slot-scope="scope">
          <span>{{ scope.row.driverName }}</span>
        </template>
      </el-table-column>
      <el-table-column label="司机手机号" align="center" show-overflow-tooltip min-width="120px">
        <template slot-scope="scope">
          <span>{{ scope.row.driverPhone }}</span>
        </template>
      </el-table-column>
      <el-table-column label="报送时间" align="center" show-overflow-tooltip min-width="170px">
        <template slot-scope="scope">
          <span>{{ scope.row.submissionTime ? dayjs(scope.row.submissionTime).format('YYYY-MM-DD HH:mm') : '' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="预计起运时间" align="center" show-overflow-tooltip min-width="170px">
        <template slot-scope="scope">
          <span>{{ scope.row.preStartTime ? dayjs(scope.row.preStartTime).format('YYYY-MM-DD HH:mm') : '' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="预计达到时间" align="center" show-overflow-tooltip min-width="170px">
        <template slot-scope="scope">
          <span>{{ scope.row.preArriveTime ? dayjs(scope.row.preArriveTime).format('YYYY-MM-DD HH:mm') : '' }}</span>
        </template>
      </el-table-column>
      <el-table-column class-name="more-list" label="商品备案编号" align="center" show-overflow-tooltip min-width="120px">
        <template slot-scope="scope">
          <div class="multi-line">
            <div v-for="(record, idx) in scope.row.goodsList" :key="idx">{{ record.productNo }}</div>
          </div>
        </template>
      </el-table-column>
      <el-table-column class-name="more-list" label="运输商品HS编号" align="center" show-overflow-tooltip min-width="140px">
        <template slot-scope="scope">
          <div class="multi-line">
            <div v-for="(record, idx) in scope.row.goodsList" :key="idx">{{ record.codeTs }}</div>
          </div>
        </template>
      </el-table-column>
      <el-table-column class-name="more-list" label="商品名称" align="center" show-overflow-tooltip min-width="120px">
        <template slot-scope="scope">
          <div class="multi-line">
            <div v-for="(record, idx) in scope.row.goodsList" :key="idx">{{ record.productName }}</div>
          </div>
        </template>
      </el-table-column>
      <el-table-column class-name="more-list" label="运输数量" align="center" show-overflow-tooltip min-width="100px">
        <template slot-scope="scope">
          <div class="multi-line">
            <div v-for="(record, idx) in scope.row.goodsList" :key="idx">{{ record.transQty }}</div>
          </div>
        </template>
      </el-table-column>
      <el-table-column class-name="more-list" label="计量单位" align="center" show-overflow-tooltip min-width="100px">
        <template slot-scope="scope">
          <div class="multi-line">
            <div v-for="(record, idx) in scope.row.goodsList" :key="idx">{{ formatter(record.gunit) }}</div>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- <div class="pagination-wrap">
      <el-pagination
        :current-page="pageable.pageNo"
        :page-sizes="[6, 20, 50, 100]"
        :page-size="pageable.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pageable.total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      >
      </el-pagination>
    </div> -->

    <div class="footer-wrap">
      <el-button size="small" @click="handleClose">关闭</el-button>
      <el-button type="primary" size="small" @click="handleConfirm">确定</el-button>
    </div>

  </el-dialog>
</template>

<script>
import dayjs from 'dayjs'
import { getTransportVehicleInfoList } from '@/api/account/index'
import dict from '@/utils/dict'
import { getDictLabel } from '@/utils/account/common'

const statusEnum = {
  status_2: '2',
  status_3: '3',
  status_4: '4'
}
const statusMap = {
  2: '有效', // 已报送、有效
  3: '已作废',
  4: '已关联其他核放单'
}

export default {
  components: {
  },
  props: {
    // 标题
    title: {
      type: String,
      default: '物流运输信息'
    },
    // 可见
    visible: {
      type: Boolean,
      default: false
    },
    // 是否多选
    multiple: {
      type: Boolean,
      default: false
    },
    // 单据类型
    decType: {
      type: String,
      default: ''
    },
    // 单据编号
    decNo: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      statusEnum,
      statusMap,
      dayjs,
      dict,
      listLoading: false,
      showValid: false,
      searchParams: {},
      infoData: {},
      allListData: [],
      listData: [],
      filterForm: {
      },
      pageable: {
        total: 0,
        pageNo: 1,
        pageSize: 6
      }
    }
  },
  computed: {
  },
  created() {
    // this.requestInfo()
  },
  methods: {
    formatter(val) {
      return getDictLabel('yp_unit', val)
    },
    rowClassName({ row }) {
      let name = ''
      if (row.status !== statusEnum.status_2) {
        name = 'row-disabled'
      }
      return name
    },
    validChange(val) {
      if (val) {
        this.listData = this.allListData.filter(i => i.status === statusEnum.status_2)
      } else {
        this.listData = this.allListData
      }
    },
    handleClose() {
      this.$emit('close')
    },
    handleConfirm() {
      const arr = this.listData.filter(i => i.checked)
      if (!arr.length) {
        this.$message.error('请选择')
        return
      }
      this.$emit('confirm', arr)
    },
    handleSizeChange(val) {
      this.pageable.pageNo = 1
      this.pageable.pageSize = val
      this.requestInfo()
    },
    handleCurrentChange(val) {
      this.pageable.pageNo = val
      this.requestInfo()
    },
    handleRowClick(row) {
      if (row.isRelated === '1') {
        return
      }
      if (this.multiple) {
        row.checked = !row.checked
      } else {
        this.listData.forEach((item) => {
          item.checked = false
        })
        row.checked = true
      }
    },
    checkboxChange(row) {
      if (this.multiple) {
        console.log('checkboxChange 多选')
      } else {
        this.listData.forEach((item) => {
          item.checked = false
        })
        row.checked = true
      }
    },
    async search(searchParams) {
      this.searchParams = searchParams
      this.pageable.pageNo = 1
      await this.requestInfo()
      return this.infoData
    },

    // 请求
    async requestInfo() {
      try {
        this.listLoading = true
        const params = {
          decNo: this.decNo,
          decType: this.decType,
          ...this.searchParams,
          ...this.filterForm,
          pageNo: this.pageable.pageNo,
          pageSize: this.pageable.pageSize
        }
        const res = await getTransportVehicleInfoList(params)
        if (res.isSuccess) {
          const data = res.data || {}
          const transportVehicleVOList = data.transportVehicleVOList || []
          const total = 0
          transportVehicleVOList.forEach((item, index) => {
            if (item.checked) {
              item.checked = true
            } else {
              item.checked = false
            }
            return item
          })
          this.infoData = data
          this.allListData = transportVehicleVOList
          this.listData = transportVehicleVOList
          this.pageable.total = total

          // 是否显示有效
          const arr = this.allListData.filter(i => i.status === statusEnum.status_2)
          if (arr.length) {
            this.showValid = true
            this.validChange(this.showValid)
          } else {
            this.showValid = false
            this.validChange(this.showValid)
          }
        }
        this.listLoading = false
      } catch (error) {
        this.listLoading = false
        console.log(error)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.pagination-wrap {
  padding: 16px 0 0 0;
}
.footer-wrap{
  padding: 20px 0 0 0;
  text-align: right;
}

::v-deep .el-form .el-form-item{
  margin: 0 30px 0 0;
}

::v-deep .el-dialog__body{
  padding: 16px;
}

::v-deep .el-table tbody td.more-list{
  padding: 0;
}

::v-deep .el-table tbody td.more-list .cell{
  padding: 0;
}
.multi-line > div {
  min-height: 21px;
  line-height: 21px;
  padding: 12px 8px;
}

.multi-line > div:not(:last-child) {
  border-bottom: 1px solid #EBEEF5;
}

::v-deep .row-disabled{
  cursor: not-allowed !important;
  background: #fafafa !important;
}

::v-deep .valid-col{
  text-align: right;
  .el-form-item{
    margin-right: 0px;
  }
}
</style>
