<template>
  <component
    :is="currentComponentName"
    v-if="currentComponentName"
    v-bind="$attrs"
    v-on="$listeners"
  >
  </component>
</template>

<script>
import { capitalizeFirstLetter } from '@/utils/index'
import AccountInventoryEntryDrawer from './AccountInventoryEntryDrawer.vue'
import AccountInventoryOutDrawer from './AccountInventoryOutDrawer.vue'
import OutboundInventoryDrawer from './OutboundInventoryDrawer.vue'

const viewTypeEnum = {
  // 账册清单（入账）。 货物入账 =》简化申报模式
  accountInventoryEntry: 'accountInventoryEntry',
  // 账册清单（出账）。
  accountInventoryOut: 'accountInventoryOut',
  // 出库清单
  outboundInventory: 'outboundInventory'
}
const componentTypeEnum = {
  drawer: 'drawer', // 抽屉
  dialog: 'dialog' // 弹窗
}

export default {
  components: {
    AccountInventoryEntryDrawer,
    AccountInventoryOutDrawer,
    OutboundInventoryDrawer
  },
  props: {
    // 预览类型
    viewType: {
      type: String,
      default: viewTypeEnum.accountInventoryEntry
    },
    // 组件类型
    componentType: {
      type: String,
      default: componentTypeEnum.drawer
    }
  },
  data() {
    return {
    }
  },
  computed: {
    currentComponentName() {
      if (!this.viewType || !this.componentType) {
        return ''
      }
      const name = `${capitalizeFirstLetter(this.viewType)}${capitalizeFirstLetter(this.componentType)}`
      return name
    }
  },
  methods: {
  }
}
</script>

<style lang="scss" scoped>
</style>
