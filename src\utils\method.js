export const filterSelect = (list, type) => {
  const data = list.map((item) => {
    return type
      ? { label: `${item.dictValue}-${item.dictLabelOrig}`, value: item.dictValue, key: item.dictLabelOrig }
      : { label: item.dictLabelOrig, value: item.dictValue }
  })
  return data
}
export const returnDictionaryName = (list, code, type) => {
  let text = ''
  list.forEach((item) => {
    if (item.code === code) text = type ? `${item.code}-${item.name}` : item.name
  })
  return text
}
export const statusReturnText = (key, cancel, approve) => {
  let text
  switch (key) {
    case 'Auditing':
      text = approve === 'y' ? '申请变更海关审核中' : '海关审核中'
      break
    case 'Passed':
      // text = approve === 'y' ? '变更审核通过' : '海关审核通过'
      text = '海关审核通过'
      break
    case 'Rejected':
      text = approve === 'y' ? '变更失败' : '海关退回'
      break
    case 'Canceling':
      text = '申请作废海关审核中'
      break
    case 'Canceled':
      text = '已作废'
      break
    case 'Draft':
      text = '待提交'
      break
    case 'Check':
      text = '查验'
      break
    case 'CancelFailed':
      text = '撤单失败'
      break
    case 'CarPassed':
      text = '车辆过卡'
      break
    case 'CheckFailToPersonDel':
      text = '查验异常，线下人工处置'
      break
    case 'ConcertCustomsDel':
      text = '配合海关查验'
      break
    default:
      text = '-'
      break
  }
  return cancel && key === 'Passed' ? '作废失败' : text
}

export const initHybUrl = () => {
  // location.href = 'https://test-psp.singlewindow.hn.cn/special-ent/hyb/login?requestUrl=https://ucs-sso-dev.digitalhainan.com.cn/singlewindow-login'
  const NODE_ENV = process.env.NODE_ENV
  console.log('NODE_ENV=', NODE_ENV)

  const baseUrl = 'https://test-psp.singlewindow.hn.cn' // NODE_ENV === 'development' ? 'https://test-psp.singlewindow.hn.cn' : 'https://psp.singlewindow.hn.cn'
  const ssoUrl = 'https://ucs-sso-dev.digitalhainan.com.cn' // NODE_ENV === 'development' ? 'https://ucs-sso-dev.digitalhainan.com.cn' : 'https://ucs-sso.digitalhainan.com.cn'
  const backUrl = `${baseUrl}/yqyc-ent/master/index.html#/filing/enterprise/list`
  // location.href = `${ssoUrl}/login?backUrl=${encodeURIComponent(backUrl)}`

  location.href = `https://ucs-sso-dev.digitalhainan.com.cn/singlewindow-login?backUrl=${encodeURIComponent(
    'https://test-psp.singlewindow.hn.cn/yqyc-ent/master/index.html#/filing/enterprise/list'
  )}`

  // location.href = process.env.NODE_ENV === 'development' ? `https://ucs-sso-dev.digitalhainan.com.cn/login?backUrl=${encodeURIComponent('https://test-psp.singlewindow.hn.cn/sdqysp-ent/#/sdsp/index')}` :
  //         `https://ucs-sso.digitalhainan.com.cn/login?backUrl=${encodeURIComponent('https://psp.singlewindow.hn.cn/sdqysp-ent/#/sdsp/index')}`
}

// 获取当前环境
export const getNodeEnv = () => {
  const env = process.env.NODE_ENV
  let ret = 'local'
  if (env === 'development' && window.location.host.includes('test')) {
    ret = 'dev'
  } else if (env === 'production') {
    ret = 'prod'
  }
  return ret
}

export const getEntUrl = () => {
  let callbackUrl = location.origin + `${location.pathname}#/`
  let sso = 'https://ucs-sso.digitalhainan.com.cn'
  if (window.location.origin.includes('test-admin-psp')) {
    callbackUrl = 'https://test-admin-psp.singlewindow.hn.cn/yqyc-ent/#/home'
    sso = 'https://ucs-sso-dev.digitalhainan.com.cn'
  }
  if (window.location.origin.includes('test-psp')) {
    if (location.pathname === '/yqyc-ent/master/index.html') {
      callbackUrl = 'https://test-psp.singlewindow.hn.cn/yqyc-ent/master/index.html#/home'
      sso = 'https://ucs-sso-dev.digitalhainan.com.cn'
    } else {
      // 私域生产仿真
      callbackUrl = 'https://test-psp.singlewindow.hn.cn/zc-ent/#/home'
      sso = 'https://ucs-sso-dev.digitalhainan.com.cn'
    }
  }

  return `${sso}/login?backUrl=${encodeURIComponent(callbackUrl)}`
}
