<template>
  <div>
    <el-input
      :value="inputValue.trim()"
      :class="{ 'centered-input': isFocused, 'left-input': !isFocused, 'is-no-empty': !!inputValue.trim() }"
      placeholder="请输入内容"
      @input="handleInput"
      @focus="handleFocus"
      @blur="handleBlur"
    >
    </el-input>
  </div>
</template>

<script>
export default {
  props: {
    inputValue: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      data: '',
      isFocused: false
    }
  },
  methods: {
    handleFocus() {
      setTimeout(() => {
        this.isFocused = true
      }, 100)
    },
    handleInput(value) {
      this.$emit('input', value)
      console.log(this.$parent)
      // this.$emit('input', value)
    },
    handleBlur() {
      setTimeout(() => {
        this.isFocused = false
      }, 100)
    }
  }
}
</script>

<style lang="scss">
.is-disabled {
  .el-input__inner {
    background-color: #F5F7FA !important;
  }
}
// .is-no-empty {
//   .el-input__inner {
//     background-color: #fff !important;
//   }
// }
.centered-input .el-input__inner {
  // text-align: center;
}

.left-input .el-input__inner {
  // text-align: left;
}
</style>
