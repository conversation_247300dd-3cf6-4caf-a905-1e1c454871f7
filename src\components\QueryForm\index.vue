<template>
  <el-form ref="form" :model="form" :disabled="disabled" label-position="right" :label-width="labelWidth">
    <el-row>
      <el-col v-if="conditions.displayAdd" :span="24" class="add-btn">
        <HandelBtn :btn="{ type: 'handleAdd', text: '重置', width: '65px' }" @handleAdd="onReset()"></HandelBtn>
      </el-col>
      <el-col v-for="value in conditions.items" :key="value.prop" :span="value.span">
        <el-form-item
          v-if="!value.noShow"
          :label="value.label ? `${value.label}:` : ''"
          :prop="value.prop"
          :class="value.style ? value.style : 'default'"
          :rules="
            value.rules ||
              (value.pattern
                ? {
                  required: value.required ? value.required : false,
                  message: `请输入合法的${value.label}`,
                  pattern: value.pattern
                }
                : {
                  required: value.required ? (value.showLongTerm ? !value.longTerm : value.required) : false,
                  message: `${value.label}不能为空`
                })
          "
        >
          <template v-if="value.showLabelTip" #label>
            <label>{{ value.label }}<span class="label-tip">{{ value.tip }}</span></label>
          </template>
          <el-cascader v-if="value.type === 'el-cascader'" v-model="form[value.prop]" :options="value.options" @change="handleChangeCascader(value.value, value.type, value.prop)" />
          <el-radio-group v-else-if="value.type === 'el-radio-group'" v-model="form[value.prop]">
            <el-radio v-for="sel in value.select" :key="sel.value" :label="sel.label" :value="sel.value"></el-radio>
          </el-radio-group>
          <el-autocomplete
            v-else-if="value.type === 'el-autocomplete'"
            :ref="value.prop"
            v-model="form[value.prop]"
            clearable
            :popper-class="isNo ? 'no-autocomplete' : 'have-autocomplete'"
            hide-loading
            :fetch-suggestions="
              (queryString, callback) => {
                querySearchAsync(queryString, callback, value.prop)
              }
            "
            :placeholder="value.placeholder"
            @blur="blurRequest(form, value.prop)"
            @select="
              item => {
                handleSelect(item, value.prop)
              }
            "
          >
            <template slot-scope="{ item }">
              <div class="complete">
                <a href="javascript:;" :title="`${item.value}${item.desc}`">
                  <span class="name">{{ item.value }}</span>
                  <span class="desc">{{ item.desc }}</span>
                </a>
              </div>
            </template>
          </el-autocomplete>
          <span v-else-if="value.type === 'show'" :class="value.className">
            {{ value.defaultValue }}
          </span>
          <slot v-else-if="value.type === 'slot'" :name="value.slotName" v-bind="value.scopedSlots || {}"></slot>
          <div v-else-if="value.type === 'el-upload'">
            <el-upload
              v-if="!value.status || value.status === 'save' || value.status === 'notApproved'|| value.status === 'retreat' || value.status === 'voided'"
              v-bind="value.uploadOptions"
              :headers="{ 'X-Requested-With': null }"
              :action="addressOss"
              :data="dataOss"
              :accept="value.accept"
              :file-list="value.prop && form[value.prop] ? form[value.prop] : []"
              :before-upload="uploadFile"
              :on-success="(res, file, fileList) => uploadSuccess(res, file, fileList, value.prop)"
              :on-error="uploadError"
              :on-preview="onPreview"
              :on-remove="
                (file, files) => {
                  handleRemove(file, files, value.prop)
                }
              "
            >
              <div v-if="value.uploadTip" slot="tip" class="upload-tip" v-html="value.uploadTip">
              </div>

              <div style="padding-right: 100px;">
                <el-button size="small" type="primary">点击上传</el-button>
              </div>
            </el-upload>
            <div v-else class="file-list">
              <div v-for="(file, index) in form[value.prop]" :key="index" class="file-item">
                <span class="file-name" @click="openUrl(file.filePath, file.name)">{{ file.name }}</span>
              </div>
            </div>
          </div>
          <component
            :is="getElement(value.type)"
            v-else-if="!value.showLongTerm || !value.longTerm"
            :ref="value.prop"
            v-model.trim="form[value.prop]"
            v-bind="value.elOptions || {}"
            :type="value.eltype ? value.eltype : null"
            :maxlength="value.maxlength"
            :multiple="value.multiple"
            :rows="value.row"
            :placeholder="value.disabled ? '-' : value.placeholder"
            :range-separator="value.rangeSeparator"
            :start-placeholder="value.startPlaceholder"
            :end-placeholder="value.endPlaceholder"
            :disabled="value.disabled"
            :allow-create="value.allowCreate"
            :filterable="value.filterable"
            :clearable="value.clearable"
            value-format="yyyy-MM-dd"
            :default-first-option="value.defaultFirstOption"
            :popper-append-to-body="!value.popperAppendToBody"
            :picker-options="value.pickerOptions || setPickerOptions(value.prop)"
            :autosize="value.autosize"
            :resize="value.resize"
            @change="changeCondition($event, value.type, value.prop)"
          >
            <el-button v-if="value.withBtnSearch" slot="append" icon="el-icon-search" @click="onwithBtnSearch(value.prop)"></el-button>
            <component
              :is="getElement(value.seltype)"
              v-for="(sel, idx) in value.select"
              :key="idx"
              :label="sel.label"
              :value="sel.value"
            ></component>
          </component>
          <el-checkbox
            v-if="value.showLongTerm"
            v-model="value.longTerm"
            label="长期"
            style="margin-left: 10px"
          ></el-checkbox>
          <span v-if="value.unit" class="unit">{{ value.unit }}</span>
          <el-tooltip v-if="value.tip" class="item" effect="light" :content="value.tip" placement="top">
            <i class="el-icon-warning icon"></i>
          </el-tooltip>
        </el-form-item>
      </el-col>
      <el-col v-if="conditions.rangDate" :span="conditions.rangDate.span" class="daterange">
        <el-form-item :label="`${conditions.rangDate.label}:`" :prop="conditions.rangDate.propStartAt">
          <el-date-picker
            v-model="form[conditions.rangDate.propStartAt]"
            type="date"
            value-format="yyyy-MM-dd"
            :clearable="false"
            :placeholder="conditions.rangDate.startPlaceholder"
          >
          </el-date-picker>
        </el-form-item>
        <span class="symbol">-</span>
        <el-form-item class="rightdate" :prop="conditions.rangDate.propEndAt">
          <el-date-picker
            v-model="form[conditions.rangDate.propEndAt]"
            type="date"
            value-format="yyyy-MM-dd"
            :clearable="false"
            :placeholder="conditions.rangDate.endPlaceholder"
          >
          </el-date-picker>
        </el-form-item>
      </el-col>
      <el-col v-if="conditions.displayBtn" :span="conditions.displayBtnSpan ? conditions.displayBtnSpan : 8">
        <el-form-item class="btns">
          <el-button type="primary" size="mini" class="searchBtn" @click="onSubmit()">查询</el-button>
          <el-button size="mini" class="searchBtn" @click="onReset()">重置</el-button>
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>
<script>
import HandelBtn from '@/components/HandleBtn/index'
import { getFileUploadPolicy, getFileViewUrl } from '@/api/service'
// import { getFileViewUrl } from '@/api/apply'

export default {
  components: { HandelBtn },
  props: {
    labelWidth: {
      type: String,
      default: '150px'
    },
    disabled: {
      type: Boolean,
      default: false
    },
    queryResults: {
      type: Array,
      default() {
        return []
      }
    },
    conditions: {
      type: Object,
      default() {
        return {
          field: {},
          items: []
        }
      }
    }
  },
  data() {
    return {
      form: {},
      pickerOptions: {},
      cb: null,
      isNo: false,
      filePath: '',
      addressOss: '',
      dataOss: {}
    }
  },
  watch: {
    conditions: {
      handler(newCond, oldCond) {
        this.form = newCond.field
      },
      immediate: true,
      deep: true
    },
    form: {
      handler(val) {
        this.$emit('getField', val)
      },
      immediate: true,
      deep: true
    },
    queryResults(newRes, oldRes) {
      if (newRes[0]) this.isNo = newRes[0].noData
      if (this.cb) {
        this.cb(newRes)
      }
    }
  },
  beforeMount() {
    this.form = this.conditions.field
    // if (this.$attrs['custom-options']) {
    //   this.$attrs['custom-options'].forEach(item => {
    //     const condition = this.conditions.items[item.index]
    //     this.conditions.items[item.index] = {
    //       ...condition,
    //       ...item.value
    //     }
    //   })
    // }
  },
  methods: {
    openUrl(fileUrl, fileName) {
      getFileViewUrl({ fileUrl, fileName })
        .then(res => {
          if (res.success) {
            window.open(res.data)
          } else {
            this.$message.error(res.message || '获取文件地址失败')
          }
        })
        .catch(() => {
          this.$message.error('获取文件地址失败')
        })
    },
    async uploadFile(file) {
      const type = file.name.substring(file.name.lastIndexOf('.') + 1)
      const params = {
        imageType: 'equipment',
        fileType: type || ''
        // interfaceName: 'com.alibaba.ewtp.psp.service.api.OSSConfigServiceService',
        // methodName: 'getFileUploadPolicy',
        // version: '1.0'
      }
      await getFileUploadPolicy(params).then(res => {
        // const type = file.name.substring(file.name.lastIndexOf('.') + 1)
        // const fileName = uuidv4() + '.' + type
        // this.fileName = fileName
        // this.filePath = res.data.dir ? `${res.data.dir}/${fileName}` : fileName
        this.filePath = res.data.fileName
        this.addressOss = this.$replaceDomain(res.data.host)
        this.dataOss = {
          key: this.filePath,
          policy: res.data.policy,
          AWSAccessKeyId: res.data.accessKeyId,
          Signature: res.data.signature
        }
      })
    },
    getElement(element) {
      return element
    },
    setPickerOptions(key) {
      const dates = ['purchaseDate', 'storageTime']
      if (dates.indexOf(key) !== -1) {
        return {
          disabledDate(time) {
            return time.getTime() > Date.now()
          }
        }
      }
      if (key === 'outTime') {
        return {
          disabledDate(time) {
            return time.getTime() < Date.now() - 8.64e7
          }
        }
      }
    },
    blurRequest(form, prop, checkRecord) {
      this.$emit('blurRequest', form, 444444)
    },
    changeCondition(e, type, name) {
      this.$emit('onChange', e, type, name)
    },
    onwithBtnSearch(prop) {
      this.$emit('onwithBtnSearch', prop)
    },
    handleChangeCascader(e, type, name) {
      this.$emit('onChangeCascader', e, type, name)
    },
    onSubmit() {
      let isValid = false
      if (this.form.statuses === '') this.form.statuses = null
      this.$refs.form.validate((valid, object) => {
        if (valid) {
          this.$emit('onSubmit', this.form)
          isValid = this.form
        } else {
          if (Object.keys(object) && Object.keys(object)[0]) {
            this.$refs[Object.keys(object)[0]][0].focus()
          }
          return false
        }
      })
      return isValid
    },
    // 查询表单不需要校验
    handleSearch() {
      return this.form
    },
    onReset() {
      Object.keys(this.form).forEach(key => {
        this.form[key] = null
      })
      this.$refs.form.clearValidate()
      this.$refs.form.resetFields()
      this.$emit('onReset', this.form)
    },
    validate() {
      return new Promise((resolve, reject) => {
        this.$refs.form.validate((valid, object) => {
          if (valid) {
            resolve()
          } else {
            if (Object.keys(object) && Object.keys(object)[0]) {
              this.$refs[Object.keys(object)[0]][0].focus()
            }
          }
        })
      })
    },
    uploadSuccess(res, file, fileList, prop) {
      if (file.status === 'success') {
        this.form[prop].push(
          {
            filePath: this.filePath,
            name: file.name
          }
        )
      } else {
        this.$message.error(res.message || '文件上传失败，请稍后重试')
      }
    },
    handleRemove(file, fileList, prop) {
      if (file.status === 'success') {
        this.form[prop] = this.form[prop].filter(item => item.filePath !== file.filePath)
      } else {
        this.$message.error('文件删除失败，请稍后重试')
      }
    },
    uploadError(err, file, fileList) {
      this.$message.error(err.message || '文件上传失败，请稍后重试')
    },
    onPreview(file) {
      getFileViewUrl({ fileUrl: file.filePath, fileName: file.name })
        .then(res => {
          if (res.success) {
            window.open(res.data)
          } else {
            this.$message.error(res.message || '获取文件地址失败')
          }
        })
        .catch(() => {
          this.$message.error('获取文件地址失败')
        })
    },
    querySearchAsync(queryString, cb, type) {
      this.$emit('querySearch', queryString, type)
      this.cb = cb
    },
    handleSelect(item, type) {
      this.$emit('selectResult', item, type)
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .el-input__inner,
::v-deep .el-form-item__label,
::v-deep .el-textarea__inner,
::v-deep .el-range-input,
::v-deep .el-range-separator,
::v-deep .el-range__icon,
::v-deep .el-radio__label,
::v-deep .el-checkbox__label {
  font-size: 14px;
}
.label-tip {
  color: #f32c43;
  text-align: left;
}
.upload-tip {
  font-size: 12px;
  color: #f32c43;
}
.file-list {
  .file-item {
    color: #606266;
    .file-name {
      margin-right: 15px;
      font-size: 12px;
      cursor: pointer;
      &:hover {
        color: #288dfd;
      }
    }
    .el-icon-download {
      font-size: 16px;
      vertical-align: -4px;
      cursor: pointer;
      &:hover {
        color: #288dfd;
      }
    }
  }
}
.icon {
  position: absolute;
  top: 35%;
  margin-left: 5px;
  color: #e6a23c;
  float: right;
  cursor: pointer;
}
.unit {
  position: absolute;
  top: 12px;
  display: inline-block;
  width: 30px;
  left: 220px;
  font-size: 12px;
  color: #666;
  line-height: 14px;
}
.btns {
  display: flex;
  justify-content: flex-end;
  ::v-deep .el-button--mini {
    padding: 7px 13px;
  }
  ::v-deep .el-form-item__content {
    margin-left: 0 !important;
  }
  .searchBtn{
    height: 32px;
  }
}
.add-btn {
  margin-bottom: 15px;
}
.maxlength {
  ::v-deep .el-form-item__content {
    width: calc(50% + 360px);
  }
  ::v-deep .el-input__inner,
  .el-select {
    width: 100%;
  }
}
.maxlength_1 {
  margin-bottom: 15px;
  ::v-deep .el-input__inner {
    height: 32px;
    line-height: 32px;
  }
  // ::v-deep .el-form-item__content {
  //   width: calc(50% + 430px);
  // }
  ::v-deep .el-input__inner,
  .el-select {
    width: 100%;
  }
}
.declaration {
  ::v-deep .el-input__inner {
    height: 32px;
    line-height: 32px;
  }
  ::v-deep .el-input__inner,
  ::v-deep .el-date-editor,
  ::v-deep .el-select {
    width: 100%;
  }
  ::v-deep .el-form-item {
    margin-bottom: 20px;
  }
}
.default {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  ::v-deep .el-input__inner {
    height: 32px;
    line-height: 32px;
  }
  ::v-deep .el-input__inner,
  ::v-deep .el-date-editor,
  ::v-deep .el-select {
    width: 200px;
  }
  ::v-deep .el-range-separator,
  ::v-deep .el-range__icon,
  ::v-deep .el-range__close-icon,
  ::v-deep .el-radio__label,
  ::v-deep .el-checkbox__label {
    line-height: 20px;
  }
  ::v-deep .el-tag--small {
    height: 20px;
    line-height: 19px;
  }
  ::v-deep .el-form-item__label {
    line-height: 15px;
  }
  ::v-deep .el-form-item__content {
    margin-left: 0 !important;
  }
}
.application {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  ::v-deep .el-input__inner,
  ::v-deep .el-date-editor,
  ::v-deep .el-select {
    width: 225px;
  }
  ::v-deep .el-form-item__content {
    margin-left: 0 !important;
  }
  ::v-deep .el-input__inner {
    height: 32px;
    line-height: 32px;
  }
  ::v-deep .el-form-item__label {
    line-height: 15px;
  }
}
.daterange {
  display: flex;
  align-items: center;
  .symbol {
    padding: 0 12px;
    margin-bottom: 15px;
    font-size: 16px;
    color: #909399;
  }
  ::v-deep .el-form-item {
    margin-bottom: 15px;
    width: 100%;
  }
  ::v-deep .el-input__inner {
    height: 32px;
    line-height: 32px;
  }
  ::v-deep .el-input__inner,
  ::v-deep .el-date-editor {
    width: 120px;
    padding-right: 0;
  }
  .rightdate {
    ::v-deep .el-form-item__content {
      margin-left: 0 !important;
    }
  }
}
.have-autocomplete {
  li {
    line-height: normal;
    padding: 7px;
    display: flex;
    align-items: center;
    .complete {
      overflow: hidden;
      text-overflow: ellipsis;
    }
    .desc {
      font-size: 12px;
      color: #b4b4b4;
    }
    .highlighted .addr {
      color: #ddd;
    }
  }
}
.no-autocomplete {
  li {
    line-height: normal;
    padding: 7px;
    display: flex;
    align-items: center;
    .complete {
      cursor: default;
      text-align: center;
    }
    .desc {
      font-size: 12px;
      color: #b4b4b4;
    }
    .highlighted .addr {
      color: #ddd;
    }
  }
}
</style>
