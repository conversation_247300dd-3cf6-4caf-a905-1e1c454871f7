<template>
  <el-drawer :append-to-body="false" class="drawer" size="100%" style="position: absolute" :modal="false"
    :wrapper-closable="false" :visible.sync="drawerVisible" :direction="direction" @before-close="handleClose">
    <div class="drawer-close">
      <div class="details-text">详情</div>
      <el-button type="primary" @click="handleClose">返回</el-button>
    </div>
    <div v-if="tipVisible" class="tip-wrap">
      <i class="el-icon-info"></i>
      <div class="text">当前单证状态为变更审核中，使用变更前数据计算库存，详情中展示变更后数据供参考。</div>
      <i class="el-icon-close" @click="tipVisible = false"></i>
    </div>
    <div class="box-collapse" style="background: #f5f5f5">
      <div class="box-collapse-box">
        <el-collapse v-model="activeNames" class="collapse" @change="handleChange">
          <el-collapse-item :name="formData.title">
            <template #title>
              <sm-title :title="formData.title" />
            </template>
            <page-form ref="mainForm" class="mt-16" :options="formOption" :form-data="formData"></page-form>
          </el-collapse-item>
        </el-collapse>
        <el-collapse v-model="activeNames1" class="collapse mt-16" @change="handleChange">
          <el-collapse-item v-if="formData1.title" :name="formData1.title">
            <template #title>
              <sm-title :title="formData1.title" />
            </template>
            <page-table ref="pageTable1" :checkbox="false" height="auto" :url="formData1.url"
              :search-params="formData1.searchValue"
              :columns="queryColumn1 && queryColumn1.length > 0 ? queryColumn1 : formData1.queryColumn"
              @cellClick="cellClick($event, 'pageTable1', 'formData1')" @selectChange="handleMainSelect">
            </page-table>
            <template>
              <div class="mt-16">
                <page-form ref="mainForm1" :options="formOption1" :form-data="formData1"></page-form>
              </div>
            </template>
          </el-collapse-item>
        </el-collapse>
        <el-collapse v-if="formData2?.title" v-model="activeNames2" class="collapse mt-16" @change="handleChange">
          <el-collapse-item v-if="formData2?.title" :name="formData2.title">
            <template #title>
              <sm-title :title="formData2.title" />
            </template>
            <page-table ref="pageTable2" :checkbox="false" height="auto" :url="formData2.url"
              :search-params="formData2.searchValue" :columns="formData2.queryColumn"
              @cellClick="cellClick($event, 'pageTable2', 'formData2')" @selectChange="handleMainSelect">
            </page-table>
            <template>
              <div class="mt-16">
                <page-form ref="mainForm2" :options="formOption2" :form-data="formData2"></page-form>
              </div>
            </template>
          </el-collapse-item>
        </el-collapse>
        <el-collapse v-if="formData3?.title" v-model="activeNames3" class="collapse mt-16" @change="handleChange">
          <el-collapse-item v-if="formData3?.title" :name="formData3.title">
            <template #title>
              <sm-title :title="formData3.title" />
            </template>
            <page-table ref="pageTable3" :checkbox="false" height="auto" :url="formData3.url"
              :search-params="formData3.searchValue" :columns="formData3.queryColumn"
              @cellClick="cellClick($event, 'pageTable3', 'formData3')" @selectChange="handleMainSelect">
            </page-table>
            <template>
              <div class="mt-16">
                <page-form ref="mainForm3" :options="formOption3" :form-data="formData3"></page-form>
              </div>
            </template>
          </el-collapse-item>
        </el-collapse>
        <el-collapse v-if="formData4?.title" v-model="activeNames4" class="collapse mt-16" @change="handleChange">
          <el-collapse-item v-if="formData4?.title" :name="formData4.title">
            <template #title>
              <sm-title :title="formData4.title" />
            </template>
            <page-table ref="pageTable4" :checkbox="false" height="auto" :url="formData4.url"
              :search-params="formData4.searchValue" :columns="formData4.queryColumn"
              @cellClick="cellClick($event, 'pageTable4', 'formData4')" @selectChange="handleMainSelect">
            </page-table>
            <template>
              <div class="mt-16">
                <page-form ref="mainForm4" :options="formOption4" :form-data="formData4"></page-form>
              </div>
            </template>
          </el-collapse-item>
        </el-collapse>
        <el-collapse v-if="formData5?.title" v-model="activeNames5" class="collapse" @change="handleChange">
          <el-collapse-item v-if="formData5?.title" :name="formData5.title">
            <template #title>
              <sm-title :title="formData5.title" />
            </template>
            <page-table ref="pageTable5" :checkbox="false" height="auto" :url="formData5.url"
              :search-params="formData5.searchValue" :columns="formData5.queryColumn"
              @cellClick="cellClick($event, 'pageTable5', 'formData5')" @selectChange="handleMainSelect">
            </page-table>
            <template>
              <div class="mt-16">
                <page-form ref="mainForm5" :options="formOption5" :form-data="formData5"></page-form>
              </div>
            </template>
          </el-collapse-item>
        </el-collapse>
        <el-collapse v-if="formData6?.title" v-model="activeNames6" class="collapse mt-16" @change="handleChange">
          <el-collapse-item v-if="formData6?.title" :name="formData6.title">
            <template #title>
              <sm-title :title="formData6.title" />
            </template>
            <page-table ref="pageTable6" :checkbox="false" height="auto" :url="formData6.url"
              :search-params="formData6.searchValue" :columns="formData6.queryColumn"
              @cellClick="cellClick($event, 'pageTable6', 'formData6')" @selectChange="handleMainSelect">
            </page-table>
            <template>
              <div class="mt-16">
                <page-form ref="mainForm6" :options="formOption6" :form-data="formData6"></page-form>
              </div>
            </template>
          </el-collapse-item>
        </el-collapse>
        <el-collapse v-if="formData7?.title" v-model="activeNames7" class="collapse mt-16" @change="handleChange">
          <el-collapse-item v-if="formData7?.title" :name="formData7.title">
            <template #title>
              <sm-title :title="formData7.title" />
            </template>
            <page-table ref="pageTable7" :checkbox="false" height="auto" :url="formData7.url"
              :search-params="formData7.searchValue" :columns="formData7.queryColumn"
              @cellClick="cellClick($event, 'pageTable7', 'formData7')" @selectChange="handleMainSelect">
            </page-table>
            <template>
              <div class="mt-16">
                <page-form ref="mainForm7" :options="formOption7" :form-data="formData7"></page-form>
              </div>
            </template>
          </el-collapse-item>
        </el-collapse>
        <el-collapse v-if="fileList.length" v-model="activeNames8" class="collapse mt-16" @change="handleChange">
          <el-collapse-item :name="'1'">
            <template #title>
              <sm-title title="附件列表" />
            </template>
            <account-upload ref="AccountUpload" v-model="fileList" :show-title="false"
              accept=".png,.PNG,.jpg,.JPG,.jpeg,.JPEG,.pdf,.PDF" operate-type="view" style="margin-bottom: 20px">
              <!-- <template #tip> 变更时请上传变更说明。支持上传PDF、JPG、JPEG格式文件，单个文件大小不超过10M </template> -->
            </account-upload>
          </el-collapse-item>
        </el-collapse>
      </div>
    </div>
  </el-drawer>
</template>

<script>
import { getListApi } from '@/api/account/index'
import { isEmpty } from '@/utils/account/common'
import smTitle from './smTitle.vue'

export default {
  components: { smTitle },
  props: {
    showChangeTip: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: ''
    },
    visible: {
      type: Boolean,
      default: false
    },
    width: {
      type: String,
      default: '50%'
    },
    tableData1: {
      type: Array,
      default: () => []
    },
    tableData2: {
      type: Array,
      default: () => []
    },
    tableData3: {
      type: Array,
      default: () => []
    },
    tableData4: {
      type: Array,
      default: () => []
    },
    tableData5: {
      type: Array,
      default: () => []
    },
    tableData6: {
      type: Array,
      default: () => []
    },
    tableData7: {
      type: Array,
      default: () => []
    },
    formData: {
      type: Object,
      default: () => { }
    },
    formOption: {
      type: Object,
      default: () => { }
    },
    formData1: {
      type: Object,
      default: () => { }
    },
    formOption1: {
      type: Object,
      default: () => { }
    },
    queryColumn1: {
      type: Array,
      default: () => []
    },
    formData2: {
      type: Object,
      default: () => { }
    },
    formOption2: {
      type: Object,
      default: () => { }
    },
    formData3: {
      type: Object,
      default: () => { }
    },
    formOption3: {
      type: Object,
      default: () => { }
    },
    formData4: {
      type: Object,
      default: () => { }
    },
    formOption4: {
      type: Object,
      default: () => { }
    },
    formData5: {
      type: Object,
      default: () => { }
    },
    formOption5: {
      type: Object,
      default: () => { }
    },
    formData6: {
      type: Object,
      default: () => { }
    },
    formOption6: {
      type: Object,
      default: () => { }
    },
    formData7: {
      type: Object,
      default: () => { }
    },
    formOption7: {
      type: Object,
      default: () => { }
    },
    formBlIdHead: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      fileList: [],
      direction: 'rtl',
      tipVisible: false,
      drawerVisible: false,
      activeNames: '',
      activeNames1: '',
      activeNames2: '',
      activeNames3: '',
      activeNames4: '',
      activeNames5: '',
      activeNames6: '',
      activeNames7: '',
      activeNames8: [],
      detailData: {}
    }
  },
  computed: {
    // activeNames() {
    //   const formDataObjects = [
    //     'formData',
    //     'formData1',
    //     'formData2',
    //     'formData3',
    //     'formData4',
    //     'formData5',
    //     'formData6',
    //     'formData7'
    //   ]
    //   const titles = []
    //   formDataObjects.forEach((propertyName) => {
    //     const property = this[propertyName]
    //     if (property && property.title) {
    //       titles.push(property.title)
    //     }
    //   })
    //   return titles
    // }
  },
  watch: {
    visible(val) {
      this.drawerVisible = val
    },
    formData: {
      handler(val) {
        if (val.title) {
          this.activeNames = val.title
        }
      },
      deep: true,
      immediate: true
    }
  },
  mounted() { },
  methods: {
    handleClose(done) {
      this.$emit('update:visible', false)
      this.drawerVisible = false
    },
    handleChange(val) { },
    async modalActiveHandler(url) {
      try {
        await getListApi(url || this.formData.url, { blId: this.formBlIdHead }).then((res) => {
          this.detailData = res.data
          for (const i in this.formData.formValue) {
            this.formData.formValue[i] = res.data[i]
          }
          this.fileList = []
          // TEST
          // res.data.attachments = [{"key":"11月份发票-吴坤育.pdf","orgName":"staging/27e96c007f7249488bcdf4fb88a65cef.pdf"}]
          if (res.data && res.data.attachments) {
            this.fileList = res.data.attachments.map((item) => ({
              name: item.orgName,
              fileName: item.orgName,
              filePath: item.key
            }))
          }

          // 展示变更中的提示
          if (this.showChangeTip && ['12', '13'].includes(this.detailData.status)) {
            this.tipVisible = true
          }

          // 详情清掉提示
          this.$nextTick(() => {
            this.$refs.mainForm && this.$refs.mainForm.$refs.ruleFormRef.clearValidate()
            this.$refs.mainForm1 && this.$refs.mainForm1.$refs.ruleFormRef.clearValidate()
            this.$refs.mainForm2 && this.$refs.mainForm2.$refs.ruleFormRef.clearValidate()
            this.$refs.mainForm3 && this.$refs.mainForm3.$refs.ruleFormRef.clearValidate()
          })
        })
      } catch (error) {
        console.log(error)
      }
      console.log('this.formData1', this.formData1)
      console.log('this.formData2', this.formData2)

      for (let i = 1; i <= 7; i++) {
        const formDataKey = `formData${i}`
        const pageTableRef = `pageTable${i}`
        if (this[formDataKey] && this[formDataKey].url && this[formDataKey].title) {
          this[formDataKey].searchValue.blIdHead = this.formBlIdHead
          this.$refs[pageTableRef]
            .getTableList()
            .then((res) => {
              this.$nextTick(() => {
                if (!isEmpty(this.$refs[pageTableRef].tableColumnData[0])) {
                  for (const key in this[formDataKey].formValue) {
                    this[formDataKey].formValue[key] = this.$refs[pageTableRef].tableColumnData[0][key]
                  }
                } else {
                  for (const key in this[formDataKey].formValue) {
                    this[formDataKey].formValue[key] = null
                  }
                }
              })
            })
            .catch((error) => {
              console.error('获取表格数据失败:', error)
            })
        }
      }
    },
    handleMainSelect(selections) { },
    cellClick(event, ref, formTab) {
      for (const i in this[formTab].formValue) {
        this[formTab].formValue[i] = event.row[i]
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.collapse {
  padding: 0 !important;
}

:deep().el-collapse-item__wrap {
  padding: 0 16px;
}

:deep().el-collapse-item__header {
  font-size: 16px !important;
  // position: relative;
  // padding-left: 16px;
  border-bottom: 2px solid #2c83e9;
}

.title-container {
  border: none;
}

//   &::before {
//     content: '';
//     display: block;
//     width: 8px;
//     height: 20px;
//     border-radius: 4px;
//     background-color: #2c83e9;
//     position: absolute;
//     left: 0px;
//     top: calc(100% - 10px) / 2;
//   }
// }

::v-deep .el-drawer__body {
  display: flex;
  flex-direction: column;
  overflow: auto;
}

.tip-wrap {
  display: flex;
  align-items: center;
  background: #e6f4ff;
  color: #202225;
  border: 1px solid #b9e0ff;
  padding: 8px 16px;
  border-radius: 8px;
  overflow: hidden;
  margin: 10px;

  .text {
    flex: 1;
    font-size: 14px;
  }

  .el-icon-info {
    color: #1578ff;
    padding-right: 10px;
  }

  .el-icon-close {
    color: #7e858b;
    cursor: pointer;
  }
}
</style>
