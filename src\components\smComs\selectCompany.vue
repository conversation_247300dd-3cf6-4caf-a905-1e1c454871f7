<template>
  <div class="select-company" v-if="companyList.length">
    <span>{{ label }}：</span>
    <el-select v-model="companySccd" clearable placeholder="请选择企业">
      <el-option
        v-for="item in companyList"
        :key="item.applyCompanySccd"
        :label="item.applyCompanyName"
        :value="item.applyCompanySccd"
      ></el-option>
    </el-select>
  </div>
</template>

<script>
import dict from '@/utils/dict'
import { mapState } from 'vuex'
export default {
  name: 'selectCompany',
  props: {
    label: {
      type: String,
      default: '申报企业'
    },
    entrustContent: {
      type: String,
      default: dict.entrustContent.enum.zcsl
    }
  },
  data() {
    return {
      companyList: [],
      companySccd: ''
    }
  },
  watch: {
    companySccd(newVal) {
      const applyCompany = this.companyList.find((item) => item.applyCompanySccd === this.companySccd) || {}
      localStorage.setItem('applyCompany', JSON.stringify(applyCompany))
      this.$store.commit('user/SET_APPLY_COMPANY', applyCompany)
      this.$emit('change', newVal)
    },
    userInfo: {
      handler() {
        this.getCompanyList()
      },
      deep: true,
      immediate: true
    }
  },
  computed: {
    ...mapState({
      userInfo: (state) => state.user.user
    })
  },
  mounted() {
    this.getCompanyList()
  },
  methods: {
    getCompanyList() {
      if (!this.userInfo.companyName) return
      const sysDelegations = this.userInfo.sysDelegations || []
      // sysDelegations = sysDelegations.map(item=>({...item, delegateContent: '2,3,4,5,6,7,8'}))
      const companyMap = {}
      dict.entrustContent.options.forEach((item) => {
        sysDelegations.forEach((sys) => {
          if (sys.delegateContent.includes(item.value)) {
            const { customsCode, companyName, socialCreditCode } = sys
            const company = {
              applyCompanyCode: customsCode,
              applyCompanyName: companyName,
              applyCompanySccd: socialCreditCode
            }
            if (companyMap[item.value]) {
              companyMap[item.value].push(company)
            } else {
              companyMap[item.value] = [company]
            }
          }
        })
      })
      const { companyName, customsCode, companyCode, companySccd } = this.userInfo
      const list = companyMap[this.entrustContent] || []
      this.companyList = list.length
        ? [
            {
              applyCompanyName: companyName,
              applyCompanyCode: customsCode || companyCode,
              applyCompanySccd: companySccd
            },
            ...list
          ]
        : []
      if (this.companyList.length) {
        const applyCompany = localStorage.getItem('applyCompany')
        if (
          applyCompany &&
          JSON.parse(applyCompany).applyCompanySccd &&
          this.companyList.findIndex((item) => item.applyCompanySccd === JSON.parse(applyCompany).applyCompanySccd) > -1
        ) {
          this.companySccd = JSON.parse(applyCompany).applyCompanySccd
        } else {
          this.companySccd = this.companyList[0].applyCompanySccd
        }
      } else {
        this.companySccd = ''
        this.$store.commit('user/SET_APPLY_COMPANY', {})
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.select-company {
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
