<template>
  <div class="title-container">
    <div class="content">
      <div class="title-left">
        <img src="@/assets/images/title-icon.png" alt="" />
        <span>{{ title }}</span>
      </div>
      <slot name="collapse"></slot>
    </div>
  </div>
</template>

<script>
export default {
  name: 'SmTitle',
  props: {
    title: {
      type: String,
      default: '标题'
    }
  },
  data() {
    return {}
  }
}
</script>

<style lang="scss" scoped>
.title-container {
  background-color: #fff;
  border-bottom: 1px solid #3366ff;
  .content {
    height: 46px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 24px;
    font-weight: 500;
    font-size: 16px;
    color: rgba(0, 0, 0, 0.85);
    img {
      width: 13px;
      height: 16px;
      margin-right: 8px;
    }
    .title-left {
        display: flex;
        align-items: center;
        justify-content: center;
    }
  }
}
</style>
