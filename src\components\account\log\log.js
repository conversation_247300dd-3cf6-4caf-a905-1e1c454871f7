import { formatTimestamp, getDictLabel } from '@/utils/account/common'

export default function logFormData($this) {
  return {
    title: '操作日志',
    descTitle: '操作日志',
    url: '/customReturnRecord',
    rule: {},
    tabs: true,
    operateColumn: true,
    urlNolist: false,
    // 表格是否显示复选框
    operatecheckbox: true,
    auditFlag: false,
    saveFlag: false,
    resetFlag: false,
    // 保存后是否关闭弹框
    closeModelAfterAdd: false,
    btn: {
      // 清除查询条件
      clear: true,
      // 查询按钮
      query: true,
      // 上传按钮
      upload: false,
      // 下载模板按钮
      downloadTemplate: false,
      // 导出按钮
      download: true,
      // 新增按钮
      add: true,
      // 编辑按钮
      edit: true,
      // 删除按钮
      delete: true,
      // 申报按钮
      declare: false,
      // 审批按钮
      audit: false,
      // 变更按钮
      change: false,
      // 作废按钮
      cancel: false,
      // 取消申请按钮
      delDeclare: false,
      // 查看按钮
      look: true,
      // 导出
      export: true,
      // 打印按钮
      print: true,
      // 操作日志按钮
      operLog: false
    },
    // 页面Tab是列表
    tabsActiveKeyList: [
      // { name: '备案', id: 'declare' },
      // { name: '变更', id: 'change' },
      // { name: '注销', id: 'cancel' }
    ],
    // 自定义的选择框点击事件
    // selectFun: (item, name) => {
    //   if(item.value=='dclMode'){
    //     // do something
    //   }
    // },
    // 单选框点击事件
    // handelRadioChange: (item, name) => {
    //   if (item.value == 'passType') {

    //   }
    // },
    // 自定义上传事件
    // afterUpload: (value) => {
    //   let data = value.response
    //   $this.formTab1.formValue.annexId = data.respData.blId
    // },
    // 输入框输入变化事件
    // handelInput: (e, name) => {
    //   if ( name == 'dcltaskDate') {

    //   }
    // },
    bottomTableShow: false,
    disabled: {
      formDisabled: false
    },
    searchValue: {
      busId: '',
      busNo: '',
      preEntryNo: ''
    },
    isOpenBtnTrue: true,
    formValue: {
      blId: '',
      busType: '',
      busId: '',
      busNo: '',
      preEntryNo: '',
      manageResult: '',
      note: '',
      opinion: '',
      dataSource: '',
      sendReceive: '',
      taskDate: '',
      gmtCreate: ''
    },
    docTypeList: [],
    bizTypeList: [],
    areaCodeList: [],
    selectFun: (item, name) => {
    },
    columns: [
      {
        key: 'preEntryNo',
        field: 'preEntryNo',
        label: '预录入编号',
        width: 230
      },
      // {
      //   key: 'busNo',
      //   field: 'busNo',
      //   label: '单据编号'
      // },
      {
        key: 'manageResult',
        field: 'manageResult',
        formatter: (scope) => {
          return getDictLabel('MANAGE_RESULT', scope)
        },
        label: '海关回执状态'
      },
      {
        key: 'note',
        field: 'note',
        label: '海关回执详情信息'
      },
      {
        key: 'taskDate',
        field: 'taskDate',
        formatter: (scope) => {
          return formatTimestamp(scope)
        },
        label: '海关回执时间'
      }
    ],
    actionColumn: {
      width: 160,
      label: '操作',
      key: 'action',
      field: 'action'
      // slots: { customRender: 'action' },
    }
  }
}
