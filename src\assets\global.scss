::v-deep .el-table {
  // font-size: 12px !important;
  thead {
    color: #000000;
    font-size: 13px;
    th {
      background: #f6f7f9;
      border-bottom: 1px solid #dcdee3;
      border-right: 1px solid #dcdee3;
      text-align: center;
      padding: 8px 0;
      &:nth-last-child(2) {
        border-right: none;
      }
      &:last-child {
        border-right: none;
      }
    }
  }
  tbody {
    color: #303133;
    td {
      text-align: center;
      padding: 8px 0;
    }
  }
  .link {
    color: #288dfd;
    cursor: pointer;
  }
}
.table-content {
  border: 1px solid #dcdee3;
  .pagination {
    padding: 15px 20px;
    display: flex;
    justify-content: flex-end;
    ::v-deep .el-input__inner {
      height: 22px;
    }
    ::v-deep .el-pagination__jump {
      margin: 0;
    }
    ::v-deep .el-pager {
      li {
        background-color: #fff;
        border: 1px solid #d2d4d9;
      }
    }
  }
}
.common-list-wrapper {
  padding-top: 30px;
  padding-left: 20px;
  padding-right: 20px;
  .btns {
    margin-bottom: 15px;
    display: flex;
    .handle-btn {
      margin-right: 15px;
    }
  }
}
.display-table {
  width: 100%;
  border: 1px solid #dcdee3;
  .display-col {
    display: flex;
    flex-wrap: wrap;
    .display-item {
      box-sizing: border-box;
      flex-wrap: wrap;
      width: 33.333%;
      height: 30px;
      display: flex;
      line-height: 30px;
      div {
        padding-left: 15px;
        padding-right: 15px;
        border-right: 1px solid #dcdee3;
        border-bottom: 1px solid #dcdee3;
      }
      .display-key {
        text-align: right;
        width: 50%;
        font-weight: 600;
      }
      .display-value {
        width: 50%;
      }
    }
  }
}
.table-common {
  background: #fcfcfd;
  border: 1px solid #e5e5e5;
  padding: 30px 0 15px 10px;
  margin-bottom: 20px;
}


