<template>
  <div v-if="display" class="handle-btn" :style="{ width: btn.width }" @click="handleClick(btn.type)">
    {{ btn.text }}
  </div>
</template>
<script>
export default {
  props: {
    btn: {
      type: Object,
      default() {
        return { type: 'handleAdd', text: '新增', width: '65px' }
      }
    },
    display: {
      type: Boolean,
      default: true
    }
  },
  methods: {
    handleClick(type) {
      this.$emit(type)
    }
  }
}
</script>
<style lang="scss" scoped>
.handle-btn {
  height: 30px;
  font-size: 12px;
  font-weight: 500;
  color: #0167ff;
  background: #ecf5ff;
  border-radius: 3px;
  border: 1px solid #0167ff;
  text-align: center;
  line-height: 30px;
  cursor: pointer;
}
</style>
