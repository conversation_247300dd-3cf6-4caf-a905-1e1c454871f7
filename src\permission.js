import router from '@/router'
import store from '@/store'
// import { Message } from 'element-ui'
import NProgress from 'nprogress' // progress bar
import 'nprogress/nprogress.css' // progress bar style
// import { getToken, setToken } from '@/utils/auth' // get token from cookie
// import getPageTitle from '@/utils/get-page-title'
// import { allowPermission } from '@/settings'

NProgress.configure({ showSpinner: false }) // NProgress Configuration

// const whiteList = ['/login', '/auth-redirect', '/login-redirect'] // no redirect whitelist

const flag = true // 比如区内是true，区外是false

router.beforeEach(async (to, from, next) => {
  // start progress bar
  NProgress.start()
  if (to.query.ticketSNO) {
    localStorage.setItem('ticketSNO', to.query.ticketSNO)
    await store.dispatch('user/getHybUserInfo2')
  }

  // set page title
  document.title = '中国（海南）国际贸易单一窗口'

  // determine whether the user has logged in
  // const hasToken = getToken()

  // if (hasToken) {
  // get user info
  if (JSON.stringify(store.getters.userInfo) === '{}') {
    await store.dispatch('user/getUserInfo')
  }
  if (to.path === '/login') {
    // if is logged in, redirect to the home page
    next()
    NProgress.done()
  } else {
    // if (allowPermission) {
    //   // determine whether the user has obtained his permission roles through getUser
    //   const hasRoutes = store.getters.permission_routes && store.getters.permission_routes.length > 0
    //   if (hasRoutes) {
    //     next()
    //   } else {
    //     try {
    //       // get user info
    //       // note: roles must be a object array! such as: ['admin'] or ,['developer','editor']
    //       const roles = store.getters.roles

    //       // generate accessible routes map based on roles
    //       const accessRoutes = await store.dispatch('permission/generateRoutes', roles)

    //       // dynamically add accessible routes
    //       router.addRoutes(accessRoutes)

    //       // hack method to ensure that addRoutes is complete
    //       // set the replace: true, so the navigation will not leave a history record
    //       next({ ...to, replace: true })
    //     } catch (error) {
    //       // remove token and go to login page to re-login
    //       await store.dispatch('user/resetToken')
    //       Message.error(error || 'Has Error')
    //       next(`/login?redirect=${to.path}`)
    //       NProgress.done()
    //     }
    //   }
    // } else {
    const hasRoutes = store.getters.permission_routes && store.getters.permission_routes.length > 0
    const menuList = ['/account/goods-off-island/out/stock/index',
       '/account/goods-off-island/out/passport/index',
      '/account/goods-off-island/cd/add-invt/query',
      '/out-link/iframe5'
      ]
    const customCreditEvaluateResult = store.state.user.user.customCreditEvaluateResult
    if (hasRoutes) {
      if (menuList.includes(to.path) && customCreditEvaluateResult === '不良信用企业') {
        next({ path: '/no-permission' })
      } else {
        next()
      }
    } else {
      const accessRoutes = await store.dispatch('permission/generateRoutes')
      // 区内路由
      const withInRoutes = ['/home', '/within/goods-off-island', '/wuliu-service', '/corpCenter', '/out-link']
      const res = await store.dispatch('user/getUser')
      const belongArea = res.data.belongArea
      // const belongArea = JSON.parse(localStorage.getItem('currentUser')).belongArea
      const flag = belongArea === '01'
      console.log(belongArea, flag, 'flag')
      const arr = await store.dispatch('permission/withInRoutes', { flag, withInRoutes })


      router.addRoutes(accessRoutes)
      next({ ...to, replace: true })
    }
    // }
  }
  // } else {
  //   /* has no token*/
  //   if (to.query?.accessToken) {
  //     const accessToken = decodeURIComponent(to.query.accessToken)
  //     setToken(accessToken)
  //     store.commit('user/SET_TOKEN', accessToken)
  //     const redirectUrl = to.query.redirect
  //     next({ path: redirectUrl || '/', replace: true })
  //     debugger
  //   } else if (whiteList.indexOf(to.path) !== -1) {
  //     // in the free login whitelist, go directly
  //     next()
  //   } else {
  //     // other pages that do not have permission to access are redirected to the login page.
  //     next(`/login?redirect=${to.path}`)
  //     NProgress.done()
  //   }
  // }
})

router.afterEach(() => {
  // finish progress bar
  NProgress.done()
})
