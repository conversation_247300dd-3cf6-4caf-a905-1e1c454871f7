#app {
  .main-container {
    min-height: 100%;
    transition: margin-left 0.28s;
    margin-left: $sideBarWidth;
    position: relative;
  }

  .sidebar-container {
    transition: width 0.28s;
    width: $sideBarWidth !important;
    background-color: $menuBg;
    height: 100%;
    position: fixed;
    font-size: 0px;
    top: 0;
    bottom: 0;
    left: 0;
    z-index: 1001;
    overflow: hidden;

    // reset element-ui css
    .horizontal-collapse-transition {
      transition: 0s width ease-in-out, 0s padding-left ease-in-out, 0s padding-right ease-in-out;
    }

    .scrollbar-wrapper {
      overflow-x: hidden !important;
    }

    .el-scrollbar__bar.is-vertical {
      right: 0px;
    }

    .el-scrollbar {
      height: 100%;
    }

    &.has-logo {
      .el-scrollbar {
        height: calc(100% - 50px);
      }
    }

    .is-horizontal {
      display: none;
    }

    a {
      display: inline-block;
      width: 100%;
      overflow: hidden;
    }

    .svg-icon {
      margin-right: 6px;
      vertical-align: -0.25em;
      pointer-events: none;
    }

    .sub-el-icon {
      margin-right: 12px;
      margin-left: -2px;
    }

    .el-menu {
      border: none;
      height: 100%;
      width: 100% !important;
    }

    .el-menu-item {
      height: 60px;
      line-height: 60px;
      padding: 0 15px !important;
      font-size: 16px;
      &:hover {
        font-weight: bold !important;
      }
    }

    .submenu-2 {
      .el-menu-item {
        position: relative;
         
        &.is-active {
          &::before {
            width: 2px;
            background-color: #3366FF;
          }
        }
      }
    }

    .el-submenu > .el-submenu__title {
      padding: 0 15px !important;
      height: 60px;
      line-height: 60px;
      &:hover {
        font-weight: bold !important;
      }
    }

    .el-submenu__title {
      font-size: 16px;
      & > span,
      & > svg,
      & > i {
        pointer-events: none;
      }
      & > i {
        color: #a0a9b5;
      }
    }

    .el-submenu__icon-arrow.el-icon-arrow-down {
      margin-top: -4px;
    }

    // menu hover
    .submenu-title-noDropdown,
    .el-submenu__title {
      &:hover {
        color: $subMenuActiveText !important;
        background-color: $menuHover !important;
        i {
          color: $subMenuActiveText !important;
        }
      }
    }

    .el-menu-item.is-active {
      background-color: $menuHover !important;
    }

    .is-active > .el-submenu__title {
      background-color: $subMenuHover !important;
      color: $subMenuActiveText !important;
      i {
        color: $subMenuActiveText !important;
      }
    }
 

    // 二级菜单
    & .nest-menu .el-submenu > .el-submenu__title,
    & .el-submenu .el-menu-item {
      min-width: $sideBarWidth !important;
      background-color: $subMenuBg !important;
      padding-left: 25px !important;
      font-size: 15px;
      &.is-active {
        color: $subMenuActiveText !important;
        i {
          color: $subMenuActiveText !important;
        }
        // background-color: $subMenuHover !important;
      }
      &:hover {
          // background-color: #fff !important;
        font-weight: bold !important;
        color: $subMenuActiveText !important;
        i {
          color: $subMenuActiveText !important;
        }
        background-color: $subMenuHover !important;
      }
      
    }

    // 三级菜单
    & .nest-menu {
      .nest-menu .el-menu-item {
        position: relative;
        height: 40px;
        line-height: 40px;
        padding-left: 41px !important;
        font-size: 14px;
        &.is-active {
          &::before {
            width: 2px;
            background-color: #3366FF;
          }
        }
        &::before {
          content: '';
          display: block;
          width: 1px;
          height: 100%;
          background-color: #C2C7CC;
          position: absolute;
          top: 0;
          left: 24px;
        }
        &:hover {
          font-weight: bold !important;
          &::before {
            content: '';
            display: block;
            width: 2px;
            height: 100%;
            background-color: #3366FF;
            position: absolute;
            top: 0;
            left: 24px;
          }
        }
      }
    }

    .submenu-3 {
      background-color: #3a414f !important;
      .el-submenu {
        &::before {
          content: '';
          display: block;
          width: 1px;
          height: 100%;
          background-color: #fff !important;
          position: absolute;
          top: 0;
          left: 24px;
        }
        .el-submenu__title {
          padding-left: 38px !important;
        }
      }
      ul {
        background-color: #3a414f !important;
      }
      li {
        background-color: #3a414f !important;
      }
    }
    .submenu-4 {
      a {
        .el-menu-item {
          .svg-icon {
            margin-left: 10px;
          }
        }
      }
      background-color: #3a414f !important;
    }
  }

  .hideSidebar {
    .sidebar-container {
      width: 60px !important;
    }

    .main-container {
      margin-left: 60px;
    }

    .submenu-title-noDropdown {
      padding: 0 !important;
      position: relative;

      .el-tooltip {
        padding: 0 !important;

        i {
          margin-left: 20px;
        }

        .sub-el-icon {
          margin-left: 19px;
        }
      }
    }

    .el-submenu {
      overflow: hidden;

      & > .el-submenu__title {
        padding: 0 !important;
        i {
          margin-left: 20px;
        }

        .sub-el-icon {
          margin-left: 19px;
        }

        .el-submenu__icon-arrow {
          display: none;
        }
      }
    }

    .el-menu--collapse {
      .el-submenu {
        & > .el-submenu__title {
          & > span {
            height: 0;
            width: 0;
            overflow: hidden;
            visibility: hidden;
            display: inline-block;
          }
        }
      }
    }
  }

  .el-menu--collapse .el-menu .el-submenu {
    min-width: $sideBarWidth !important;
  }

  // mobile responsive
  .mobile {
    .main-container {
      margin-left: 0px;
    }

    .sidebar-container {
      transition: transform 0.28s;
      width: $sideBarWidth !important;
    }

    &.hideSidebar {
      .sidebar-container {
        pointer-events: none;
        transition-duration: 0.3s;
        transform: translate3d(-$sideBarWidth, 0, 0);
      }
    }
  }

  .withoutAnimation {
    .main-container,
    .sidebar-container {
      transition: none;
    }
  }
}

// when menu collapsed
.el-menu--vertical {
  & > .el-menu {
    .svg-icon {
      margin-right: 16px;
    }
    .sub-el-icon {
      margin-right: 12px;
      margin-left: -2px;
    }
  }

  .nest-menu .el-submenu > .el-submenu__title,
  .el-menu-item {
    &:hover {
      // you can use $subMenuHover
      background-color: $menuHover !important;
    }
  }

  // the scroll bar appears when the subMenu is too long
  > .el-menu--popup {
    max-height: 100vh;
    overflow-y: auto;

    &::-webkit-scrollbar-track-piece {
      background: #d3dce6;
    }

    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-thumb {
      background: #99a9bf;
      border-radius: 20px;
    }
  }
}
