<template>
  <div class="handle-table">
    <div v-if="isEdit" class="btns">
      <div class="table-btn">
        <HandelBtn :btn="{ type: 'handleSave', text: '插入', width: '65px' }" @handleSave="handleSave"></HandelBtn>
        <HandelBtn
          class="delete"
          :btn="{ type: 'handleDelete', text: '批量删除', width: '85px' }"
          @handleDelete="handleDelete"
        ></HandelBtn>
      </div>
      <!--      <div class="upload-btn">-->
      <!--        <HandelBtn-->
      <!--          :btn="{ type: 'handleUpload', text: '料件成品Excel上传', width: '135px' }"-->
      <!--          @handleUpload="handleUpload"-->
      <!--        ></HandelBtn>-->
      <!--      </div>-->
    </div>
    <div class="table-content">
      <el-table :data="tableData" highlight-current-row @selection-change="changeSelect" @row-click="handleClickRow">
        <el-table-column v-if="isEdit || isSelection" type="selection" width="55"></el-table-column>
        <el-table-column
          v-for="item in columns"
          :key="item.prop"
          :prop="item.prop"
          :label="item.label"
          border
        ></el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination
          background
          small
          :current-page="pagination.page"
          :page-sizes="[10, 20, 30, 40]"
          :page-size="pagination.count"
          layout="total, prev, pager, next,sizes, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <UploadModal :upload-dialog="dialogVisible" :upload-src="linkSrc" @isUploadHandle="closeUploadModal" />
  </div>
</template>
<script>
import UploadModal from '@/components/UploadModal/index'
import HandelBtn from '@/components/HandleBtn/index'

export default {
  components: { UploadModal, HandelBtn },
  props: {
    columns: {
      type: Array,
      default: () => []
    },
    tableData: {
      type: Array,
      default: () => []
    },
    isEdit: {
      type: Boolean,
      default() {
        return true
      }
    },
    isSelection: {
      type: Boolean,
      default() {
        return false
      }
    },
    total: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      dialogVisible: false,
      linkSrc: 'alibaba.com',
      delIds: [],
      pagination: {
        page: 1,
        count: 10
      }
    }
  },
  methods: {
    handleCurrentChange(page) {
      this.pagination = { ...this.pagination, page }
      this.$emit('handleTableChange', this.pagination)
    },
    handleSizeChange(count) {
      this.pagination = { ...this.pagination, count, page: 1 }
      this.$emit('handleTableChange', this.pagination)
    },
    // 保存
    handleSave() {
      this.$emit('handleSubmit', 1, false)
    },
    // 多选
    changeSelect(selection) {
      this.delIds = selection.map((item) => item.id)
      this.$emit('handleSelect', this.delIds)
    },
    // 单行选择
    handleClickRow(row) {
      this.$emit('clickTableRow', row)
    },
    // 删除
    handleDelete() {
      const { delIds } = this
      if (delIds.length === 0) {
        this.$message({
          showClose: true,
          message: '批量删除至少选择一项',
          type: 'warning'
        })
        return
      }
      this.$emit('handleDelete', delIds, this.pagination)
    },
    closeUploadModal() {
      this.dialogVisible = false
    },
    handleUpload() {
      this.$message({
        showClose: true,
        message: '暂未开放'
      })
      return
      // this.dialogVisible = true
    }
  }
}
</script>
<style lang="scss" scoped>
.btns {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 15px 0;
}
.table-btn {
  display: flex;
  align-items: center;
  .delete {
    margin-left: 12px;
  }
}
.table-content {
  border: 1px solid #dcdee3;
  .pagination {
    padding: 15px 20px;
    display: flex;
    justify-content: flex-end;
    background: #ffffff;
    ::v-deep .el-input__inner {
      height: 22px;
    }
    ::v-deep .el-pagination__jump {
      margin: 0;
    }
    ::v-deep .el-pager {
      li {
        background-color: #fff;
        border: 1px solid #d2d4d9;
      }
    }
  }
}
::v-deep .el-table {
  // font-size: 12px !important;
  thead {
    color: #000000;
    font-size: 13px;
    th {
      background: #f6f7f9;
      border-bottom: 1px solid #dcdee3;
      border-right: 1px solid #dcdee3;
      text-align: center;
      padding: 8px 0;
      &:nth-last-child(2) {
        border-right: none;
      }
      &:last-child {
        border-right: none;
      }
    }
  }
  tbody {
    color: #303133;
    td {
      text-align: center;
      padding: 8px 0;
    }
  }
  .link {
    color: #288dfd;
    cursor: pointer;
  }
}
</style>
