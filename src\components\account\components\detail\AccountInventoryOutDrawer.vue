<template>
  <page-drawer
    ref="pageDrawer"
    :visible="visible"
    :form-bl-id-head="detailId"
    :form-option="formDetailsOption"
    :form-data="formData"
    :form-option1="formDetailsOption1"
    :form-data1="formData1"
    :form-option2="formDetailsOption2"
    :form-data2="formData2"
    :title="'详情'"
    :show-change-tip="true"
    @update:visible="updateVisible"
  ></page-drawer>
</template>

<script>
import AccountInvtFormData from '@/views/account/intra-island-service/sale2carryforwards/add-invt/account-invt/account-invt'
import { formOption as formDetailsOption } from '@/views/account/intra-island-service/sale2carryforwards/add-invt/account-invt/account-invt-details-options'

import AccountInvtProductFormData from '@/views/account/intra-island-service/sale2carryforwards/add-invt/account-invt-product/account-invt-product'
import { formOption as formDetailsOption1 } from '@/views/account/intra-island-service/sale2carryforwards/add-invt/account-invt-product/account-invt-product-details-options'

import AccountInvtMaterialFormData from '@/views/account/intra-island-service/sale2carryforwards/add-invt/account-invt-material/account-invt-material'
import { formOption as formDetailsOption2 } from '@/views/account/intra-island-service/sale2carryforwards/add-invt/account-invt-material/account-invt-material-details-options'

import pageDrawer from '@/components/account/pageDrawer.vue'
import { goodsTypeEnum } from './const'

export default {
  components: {
    pageDrawer
  },

  props: {
    // 可见
    visible: {
      type: Boolean,
      default: false
    },
    // 详情id
    detailId: {
      type: String,
      default: ''
    },
    // 行数据（点击列表的行数据）
    row: {
      type: Object,
      default: _ => ({
        // 货物类型
        goodsType: ''
      })
    }
  },
  data() {
    return {
      formData: new AccountInvtFormData(this),
      formDetailsOption,
      formData1: new AccountInvtProductFormData(this),
      formDetailsOption1,
      formData2: new AccountInvtMaterialFormData(this),
      formDetailsOption2
    }
  },
  computed: {
  },
  watch: {
    visible() {
      if (this.visible) {
        this.init()
      }
    }
  },
  created() {
  },
  methods: {
    updateVisible() {
      this.$emit('update:visible', false)
    },
    init() {
      if (this.formData) {
        this.formData.disabled.formDisabled = true
      }

      for (let i = 1; i <= 7; i++) {
        const formDataKey = `formData${i}`
        if (this[formDataKey] && this[formDataKey].url) {
          this[formDataKey].disabled.formDisabled = true
        }
      }

      // 判断
      if (this.row.goodsType === goodsTypeEnum.W) {
        this.formData1.title = ''
        this.formData2.title = '出账清单商品信息'
        this.formData2.queryColumn.forEach((item) => {
          if (item.key === 'uncoVersion') {
            item.hidden = true
          }
        })
      } else {
        this.formData1.title = '出账清单成品信息'
        this.formData2.title = '出账清单料件信息'
        this.formData2.queryColumn.forEach((item) => {
          if (item.key === 'uncoVersion') {
            item.hidden = false
          }
        })
      }

      this.$nextTick(() => {
        this.$refs.pageDrawer.modalActiveHandler()
      })
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
