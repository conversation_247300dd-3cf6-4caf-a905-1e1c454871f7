<template>
  <el-dialog
    title="导入记录"
    :visible.sync="dialogVisible"
    width="1040px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :show-close="false"
  >
    <div class="table-content">
      <el-table :data="tableData" style="width: 100%">
        <el-table-column
          v-for="item in IMPORTLIST"
          :key="item.prop"
          :prop="item.prop"
          :label="item.label"
          :min-width="item.width"
          border
        >
          <template slot-scope="scope">
            <el-popover>
              <span slot="reference">{{ scope.row[item.prop] }}</span>
            </el-popover>
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" width="260">
          <template slot-scope="scope">
            <el-button type="text" size="small" @click="openUrl(scope.row.fileUrl)">下载原文件</el-button>
            <el-button type="text" size="small" @click="openUrl(scope.row.errorFileUrl)">下载错误报告</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination
          background
          small
          :current-page="pagination.page"
          :page-sizes="[10, 20, 30, 40]"
          :page-size="pagination.count"
          layout="total, prev, pager, next,sizes, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="onClose()">取 消</el-button>
    </span>
  </el-dialog>
</template>

<script>
import moment from 'moment'
import QueryForm from '@/components/QueryForm/index'
import HandleBtn from '@/components/HandleBtn/index'
export default {
  name: 'ProductRecordList',
  components: { QueryForm, HandleBtn },
  props: {
    dialogVisible: {
      required: true
    },
    getListParams: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      IMPORTLIST: [
        { label: '文件名称', prop: 'fileName', width: 200 },
        { label: '版本号', prop: 'version', width: 200 },
        { label: '导入结果', prop: 'resultFlag', width: 100 },
        { label: '导入时间', prop: 'createTime', width: 200 }
      ],
      tableData: [],
      pagination: {
        page: 1,
        count: 10
      },
      total: 0
    }
  },
  mounted() {
    this.getList()
  },
  methods: {
    onClose() {
      this.$emit('onImportList', false)
    },
    openUrl(url, name) {
      const params = {
        url: '/imported/common/file/getFileViewUrl',
        fileName: name
      }
      this.execute(params).then(res => {
        if (res.success) {
          window.open(res.data, '_blank')
        } else {
          this.$message.error(res.message || '获取文件地址失败')
        }
      }).catch(e => {
        this.$message.error('获取文件地址失败')
      })
    },
    moment,
    handleCurrentChange(page) {
      this.pagination.page = page
    },
    handleSizeChange(count) {
      this.pagination = { page: 1, count: count }
    },
    getList() {
      this.loading = true
      const params = {
        ...this.getListParams,
        data: {
          ...this.pagination
        }
      }
      this.execute(params)
        .then((res) => {
          this.total = res.page.total
          this.tableData = res.data
        })
        .catch((err) => {
          this.$message({
            showClose: true,
            message: err,
            type: 'error'
          })
          this.tableData = [{
            id: '222',
            taxCode: '11',
            status: 1,
            goodName: '11',
            releasedTime: '2022-08-2',
            gmtModifiedTime: '2022-08-10',
            gmtCreateTime: '2022-08-2'
          }]
        })
        .finally(() => {
          this.loading = false
        })
    }
  }
}
</script>
<style lang="scss">
  // .el-popper[x-placement^='bottom'] .popper__arrow::after {
  //   border-bottom-color: rgba(29, 43, 53, 0.5);
  //   top: 0px;
  // }
  // .el-popover {
  //   border: 0;
  //   background: rgba(29, 43, 53, 0.5);
  //   color: #fff;
  // }
</style>
<style lang="scss" scoped>
  .table-content {
    border: 1px solid #dcdee3;
    .pagination {
      padding: 15px 20px;
      display: flex;
      justify-content: flex-end;
      ::v-deep .el-input__inner {
        height: 22px;
      }
      ::v-deep .el-pagination__jump {
        margin: 0;
      }
      ::v-deep .el-pager {
        li {
          background-color: #fff;
          border: 1px solid #d2d4d9;
        }
      }
    }
  }
</style>
