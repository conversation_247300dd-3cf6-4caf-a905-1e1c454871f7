// 该文件负责所有的公共的组件的全局注册   Vue.use
import pageContainer from './pageContainer.vue'
import pageForm from './pageForm.vue'
import pageTable from './pageTable.vue'
import pageEditTable from './pageEditTable.vue'
import pageDialog from './pageDialog.vue'
import pageCard from './pageCard.vue'
import pageDrawer from './pageDrawer.vue'
import pageTips from './pageTips.vue'
import pageSearchForm from './pageSearchForm.vue'
import pageSelect from './pageSelect.vue'
import smInput from './smInput.vue'
import smTitle from './smTitle.vue'
export default {
  // 通过install来拿到全局的Vue对象
  install(Vue) {
    //  注册全局的通用栏组件对象
    Vue.component('pageContainer', pageContainer)
    Vue.component('pageForm', pageForm)
    Vue.component('pageTable', pageTable)
    Vue.component('pageDialog', pageDialog)
    Vue.component('pageCard', pageCard)
    Vue.component('pageDrawer', pageDrawer)
    Vue.component('pageTips', pageTips)
    Vue.component('pageSearchForm', pageSearchForm)
    Vue.component('pageSelect', pageSelect)
    Vue.component('pageEditTable', pageEditTable)
    Vue.component('smInput', smInput)
    Vue.component('smTitle', smTitle)
  }
}
