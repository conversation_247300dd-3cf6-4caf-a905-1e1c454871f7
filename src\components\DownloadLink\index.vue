<template>
  <div class="download-link-wrapper">
    <i class="el-icon-paperclip"></i>
    <span class="download-link" @click="download">{{ file.name }}</span>
  </div>
</template>

<script>
export default {
  name: 'DownloadLink',
  props: {
    file: {}
  },
  methods: {
    async download() {
      if (this.file.url) {
        window.open(this.file.url)
        return false
      }
      const params = {
        url: '/common/file/getFileViewUrl',
        data: {
          fileName: this.file.fileName
        }
      }
      this.execute(params).then(res => {
        if (res.success) {
          window.open(res.data)
        } else {
          this.$message.error(res.message || '获取文件地址失败')
        }
      })
    }
  }
}
</script>

<style scoped>
.download-link-wrapper {
  font-size: 14px;
}
.download-link {
  color: #1890ff;
  text-decoration: underline;
}
</style>
