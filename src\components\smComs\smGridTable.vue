<template>
  <div class="grid-table">
    <el-row v-for="(row, rowIndex) in tableData" :key="rowIndex" :gutter="0">
      <el-col
        v-for="(col, colIndex) in columns"
        :key="colIndex"
        :span="col.span || defaultSpan"
        class="grid-cell"
      >
        <div class="cell-container">
          <div class="cell-label">
            <div class="label-content">
              {{ col.label }}
              <el-tooltip
                v-if="col.tooltip"
                effect="light"
                :content="col.tooltip"
                placement="top"
                popper-class="label-tooltip"
              >
                <i class="el-icon-info tooltip-icon"></i>
              </el-tooltip>
            </div>
          </div>
          <el-tooltip
            effect="light"
            placement="top"
            :content="row[col.prop] || '-'"
            :disabled="!isEllipsisActive[`${rowIndex}-${colIndex}`]"
            popper-class="grid-tooltip"
          >
            <div
              class="cell-value"
              :style="{color: col.valueColor}"
              @mouseenter="checkEllipsis($event, rowIndex, colIndex)"
            >
              {{ row[col.prop] || '-' }}

              <sm-tag v-if="col.tagInfo?.show" :type="col.tagInfo?.type">
                {{ col.tagInfo?.text }}
              </sm-tag>
            </div>
          </el-tooltip>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import SmTag from '@/components/SmTag/index'

export default {
  name: 'GridTable',
  components: {
    SmTag
  },
  props: {
    // 表格数据
    tableData: {
      type: Array,
      default: () => []
    },
    // 列配置
    columns: {
      type: Array,
      default: () => []
    },
    // 默认栅格跨度
    defaultSpan: {
      type: Number,
      default: 6 // 默认每行4个单元格 (24/6=4)
    }
  },
  data() {
    return {
      isEllipsisActive: {}
    }
  },
  methods: {
    checkEllipsis(e, rowIndex, colIndex) {
      const el = e.target
      this.$set(
        this.isEllipsisActive,
        `${rowIndex}-${colIndex}`,
        el.scrollWidth > el.offsetWidth
      )
    }
  }
}
</script>

  <style lang="scss">
  .grid-tooltip {
    max-width: 600px !important;
    line-height: 1.5;
    padding: 8px 12px !important;
    word-break: break-all;
    white-space: pre-wrap;
  }

  .label-tooltip {
    max-width: 300px !important;
    line-height: 1.5;
    padding: 8px 12px !important;
  }
  </style>

  <style lang="scss" scoped>
  .grid-table {
    width: 100%;
    background-color: #FFFFFF;
    border-top: 1px solid #DCDFE6;
    border-left: 1px solid #DCDFE6;

    .el-row {
      display: flex;
      flex-wrap: wrap;
      margin: 0 !important;
    }

    .grid-cell {
      padding: 0 !important;
      height: 46px;
      border-right: 1px solid #DCDFE6;
      border-bottom: 1px solid #DCDFE6;

      .cell-container {
        display: flex;
        height: 100%;

        .cell-label {
          position: relative;
          color: #606266;
          font-size: 14px;
          font-weight: bold;
          width: 146px;
          flex-shrink: 0;
          background-color: #FAFAFA;
          border-right: 1px solid #DCDFE6;
          padding: 0 8px;
          display: flex;
          align-items: center;
          justify-content: flex-end;

          .label-content {
            display: flex;
            align-items: center;
            line-height: 1;
          }

          .tooltip-icon {
            margin-left: 4px;
            color: #909399;
            font-size: 14px;
            cursor: pointer;
          }
        }

        .cell-value {
          color: #303133;
          font-size: 14px;
          flex: 1;
          padding: 0 16px;
          margin-left: 8px;
          display: block;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          line-height: 46px;
        }
      }
    }
  }
  </style>
