<template>
    <div class="base-page">
      <iframe class="iframe" width="100%" height="100%" :src="src" @load="onIframeLoad" frameborder="0"></iframe>
    </div>
  </template>
  
  <script>
  import mixin from '../mixin'
  export default {
    name: 'IframePage',
    mixins: [mixin],
    data() {
      return {
        src: `${location.origin}/wljg/baseData/driverInfo`
      }
    },
    
  
  }
  </script>
  <style lang="scss" scoped>
  .base-page {
    padding: 0;
    height: calc(100vh - 50px);
  }
  </style>
  