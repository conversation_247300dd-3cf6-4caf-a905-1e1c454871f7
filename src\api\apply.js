import request from '@/utils/request'

export function getRecordInfo(data) {
  return request({
    url: '/customer/company/record/detail',
    method: 'post',
    data
  })
}

export function retreat(data) {
  return request({
    url: '/customer/company/retreat ',
    method: 'post',
    data
  })
}

export function getApprovalRecord(data) {
  return request({
    url: '/customer/company/approvalRecord',
    method: 'post',
    data
  })
}

export function getCompanyTypes(data) {
  return request({
    url: '/customer/company/types',
    method: 'post',
    data
  })
}

export function submitApply(data) {
  return request({
    url: '/customer/company/record/save',
    method: 'post',
    data
  })
}

export function handleSubmit(data) {
  return request({
    url: '/customer/company/retreat',
    method: 'post',
    data
  })
}

export function upload(data) {
  return request({
    url: '/upload',
    method: 'post',
    data
  })
}

/**
 * 获取进口设备行业类型
 *
 * @export
 * @param {*} data
 * @returns
 */

export function getCompanyType(data) {
  return request({
    url: '/customer/company/types',
    method: 'post',
    data
  })
}

/**
 * 获取文件展示路径
 *
 * @export
 * @param {*} data
 * @returns
 */
export function getFileViewUrl(params) {
  return request({
    url: '/getFileViewUrl',
    method: 'get',
    params
  })
}
