const childProcess = require('child_process')
const packageJson = require('./package.json')

const execSync = (command) => {
  childProcess.execSync(command, { stdio: [0, 1, 2] })
}

const args = process.argv.slice(2)
let version = args[1]
const env = args[0]
const { name } = packageJson
const reg = /\d+\.\d+\.\d+/
if (version.match(reg)) {
  version = version.match(reg)[0]
}

console.log('env=', env)
console.log('version=', version)
// if(env === 'pre') {
//   execSync(`npm run build:test --env=${env}-${version} --mode=test `)
// }else {
execSync(`npm run build:prod --env=${env}-${version}`)
// }
execSync(`mkdir -p ./product/${name}/${version}`)
execSync(`mv ./dist/* ./product/${name}/${version}/`)
