<template>
  <div class="login-container">
    <div class="login-box">
      <div class="login-left">
        <img src="@/assets/login-banner.svg" alt="login banner">
      </div>
      <div class="login-right">
        <el-form ref="loginForm" :model="loginForm" :rules="loginRules" class="login-form" autocomplete="on">
          <div class="title-container">
            <h3 class="title">欢迎登录</h3>
            <p class="subtitle">WELCOME TO LOGIN</p>
          </div>

          <el-form-item prop="username">
            <span class="svg-container">
              <svg-icon icon-class="user" />
            </span>
            <el-input id="none-bg" ref="username" v-model="loginForm.username" placeholder="请输入用户名" name="username"
              type="text" tabindex="1" autocomplete="on" />
          </el-form-item>

          <el-form-item prop="password">
            <span class="svg-container">
              <svg-icon icon-class="password" />
            </span>
            <el-input id="none-bg" :key="passwordType" ref="password" v-model="loginForm.password" :type="passwordType"
              placeholder="请输入密码" name="password" tabindex="2" autocomplete="on" @keyup.enter.native="handleLogin" />
            <span class="show-pwd" @click="showPwd">
              <svg-icon :icon-class="passwordType === 'password' ? 'eye' : 'eye-open'" />
            </span>
          </el-form-item>

          <el-form-item prop="captcha" class="captcha-item">
            <span class="svg-container">
              <svg-icon icon-class="valid-code" />
            </span>
            <el-input id="none-bg" ref="captcha" v-model="loginForm.captcha" placeholder="请输入验证码" name="captcha"
              type="text" tabindex="3" @keyup.enter.native="handleLogin" />
            <div class="captcha-container" @click="refreshCaptcha">
              <img :src="captchaUrl" class="captcha-img">
              <div class="captcha-mask">
                <i class="el-icon-refresh" />
              </div>
            </div>
          </el-form-item>

          <el-button :loading="loading" type="primary" class="login-button" @click.native.prevent="handleLogin">
            {{ loading ? '登录中...' : '登录' }}
          </el-button>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script>
import { rsaEncrypt } from '@/utils/encrypt'
export default {
  name: 'Login',
  data() {
    const validateUsername = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请输入用户名'))
      } else {
        callback()
      }
    }
    const validatePassword = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请输入密码'))
      } else if (value.length < 6) {
        callback(new Error('密码不能少于6位'))
      } else {
        callback()
      }
    }
    const validateCaptcha = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请输入验证码'))
      } else if (value.length !== 4) {
        callback(new Error('请输入4位验证码'))
      } else {
        callback()
      }
    }
    return {
      loginForm: {
        username: '',
        password: '',
        captcha: ''
      },
      loginRules: {
        username: [{ required: true, trigger: 'blur', validator: validateUsername }],
        password: [{ required: true, trigger: 'blur', validator: validatePassword }],
        captcha: [{ required: true, trigger: 'blur', validator: validateCaptcha }]
      },
      passwordType: 'password',
      capsTooltip: false,
      loading: false,
      redirect: undefined,
      otherQuery: {},
      captchaUrl: ''
    }
  },
  watch: {
    $route: {
      handler: function (route) {
        const query = route.query
        if (query) {
          this.redirect = query.redirect
          this.otherQuery = this.getOtherQuery(query)
        }
      },
      immediate: true
    }
  },
  created() {
    this.refreshCaptcha()
  },
  mounted() {
    if (this.loginForm.username === '') {
      this.$refs.username.focus()
    } else if (this.loginForm.password === '') {
      this.$refs.password.focus()
    }

    this.refreshCaptcha()
  },
  methods: {
    checkCapslock(e) {
      const { key } = e
      this.capsTooltip = key && key.length === 1 && key >= 'A' && key <= 'Z'
    },
    showPwd() {
      if (this.passwordType === 'password') {
        this.passwordType = ''
      } else {
        this.passwordType = 'password'
      }
      this.$nextTick(() => {
        this.$refs.password.focus()
      })
    },
    handleLogin() {
      this.$refs.loginForm.validate(valid => {
        if (valid) {
          this.loading = true
          this.$store
            .dispatch('user/login', { ...this.loginForm, password: rsaEncrypt(this.loginForm.password)})
            .then((res) => {
               
              this.loading = false
            })
            .catch((err) => {
              this.loading = false
              this.refreshCaptcha()
              this.loginForm.captcha = ''
            })
        }
      })
    },
    getOtherQuery(query) {
      return Object.keys(query).reduce((acc, cur) => {
        if (cur !== 'redirect') {
          acc[cur] = query[cur]
        }
        return acc
      }, {})
    },
    refreshCaptcha() {
      this.captchaUrl = process.env.VUE_APP_BASE_API + '/login/verification?' + new Date().getTime()
    }
  }
}
</script>

<style lang="scss">
#none-bg {
  background: none !important;
}
</style>

<style lang="scss">
/* 修复input 背景不协调 和光标变色 */
$primary-color: #409EFF;
$bg: #fff;
$dark_gray: #606266;
$light_gray: #303133;

.login-container {
  .el-input {
    display: inline-block;
    height: 48px;
    width: 85%;

    input {
      background: transparent;
      border: 0;
      -webkit-appearance: none;
      border-radius: 0;
      padding: 12px 5px 12px 15px;
      height: 48px;
      color: $light_gray;
      caret-color: $primary-color;

      &:focus {
        box-shadow: none !important;
      }
    }
  }

  .el-form-item {
    border: none;
    border-bottom: 1px solid #DCDFE6;
    background: transparent;
    border-radius: 0;
    margin-bottom: 30px;
    transition: all 0.2s;

    &:hover {
      border-color: #C0C4CC;
    }

    &.is-error {
      border-color: #F56C6C;
    }

    &.captcha-item {
      border: 1px solid #DCDFE6;
      border-radius: 4px;

      .el-input input {
        border-right: 1px solid #DCDFE6;
      }
    }
  }
}
</style>

<style lang="scss" scoped>
$bg: #f5f7f9;
$dark_gray: #606266;
$light_gray: #303133;

.login-container {
  min-height: 100vh;
  width: 100%;
  background-color: $bg;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;

  .login-box {
    width: 1000px;
    height: 600px;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    display: flex;
    overflow: hidden;
  }

  .login-left {
    flex: 1;
    background: linear-gradient(45deg, #409EFF, #36D1DC);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40px;

    img {
      max-width: 100%;
      height: auto;
      animation: float 6s ease-in-out infinite;
    }
  }

  .login-right {
    width: 400px;
    padding: 0 35px;
    display: flex;
    align-items: center;
  }

  .login-form {
    width: 100%;
  }

  .title-container {
    margin-bottom: 40px;

    .title {
      font-size: 28px;
      color: $light_gray;
      margin: 0 0 10px;
      text-align: center;
      font-weight: bold;
    }

    .subtitle {
      font-size: 14px;
      color: #909399;
      text-align: center;
      margin: 0;
    }
  }

  .svg-container {
    padding: 6px 5px 6px 0;
    color: #909399;
    vertical-align: middle;
    width: 30px;
    display: inline-block;
  }

  .show-pwd {
    position: absolute;
    right: 5px;
    top: 12px;
    font-size: 16px;
    color: #909399;
    cursor: pointer;
    user-select: none;
  }

  .captcha-item {
    .el-input {
      width: calc(100% - 140px);
    }
  }

  .captcha-container {
    position: absolute;
    right: 1px;
    top: 1px;
    height: 46px;
    width: 120px;
    padding: 4px;
    cursor: pointer;
    overflow: hidden;
    background: #fff;
    border-radius: 0 4px 4px 0;

    &:hover .captcha-mask {
      opacity: 1;
    }
  }

  .captcha-img {
    height: 38px;
    width: 112px;
  }

  .captcha-mask {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s;

    i {
      color: #fff;
      font-size: 20px;
    }
  }

  .login-button {
    width: 100%;
    height: 48px;
    margin-top: 10px;
    border-radius: 4px;
    font-size: 16px;

    &:focus {
      outline: none;
    }
  }

  @media screen and (max-width: 1000px) {
    .login-box {
      width: 100%;
      height: 100vh;
      border-radius: 0;
    }

    .login-left {
      display: none;
    }

    .login-right {
      width: 100%;
      padding: 0 20px;
    }
  }
}

@keyframes float {
  0% {
    transform: translateY(0px);
  }

  50% {
    transform: translateY(-20px);
  }

  100% {
    transform: translateY(0px);
  }
}
</style>
