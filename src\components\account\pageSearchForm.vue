<template>
  <div class="penk-form-container searchForm">
    <el-form ref="ruleFormRef" :inline="true" :size="options.size || 'middle'" :disabled="options.formDisabled" :label-width="options.labelWidth + 'px'" :model="formData.searchValue">
      <el-row :gutter="16">
        <el-col v-for="item in options.column" :key="item.prop" :span="item.col">
          <el-form-item :label="item.label + ':'" :style="{ display: item.hidden == true ? 'none' : '' }" :prop="item.prop">
            <!-- 输入框 -->
            <el-input v-if="item.type == 'input'" v-model="formData.searchValue[item.prop]" :size="options.size || 'middle'" :placeholder="'请输入' + item.label" :clearable="item.clearable" :disabled="item.disabled" :type="item.inputType" :row="item.row" :style="{ width: item.width + 'px' }">
              <el-button v-if="item.addbtn" slot="append" type="primary" size="small" icon="el-icon-circle-plus-outline" @click="handleSearchDialog">查询</el-button>
            </el-input>
            <!-- 下拉框 -->

            <pageSelect
              v-else-if="item.type == 'select'"
              :list="item.options"
              :value="formData.searchValue[item.prop]"
              :dict-type="item.dictType"
              :fetch-type="item.fetchType"
              :exclude-option-values="item.excludeOptionValues"
              :placeholder="'请选择' + item.label"
              :clearable="item.clearable"
              :disabled="item.disabled"
              :style="{ width: item.width + 'px' }"
              @onchange="val => onChange(item.prop,val)"
            />
            <!-- 多选框 -->
            <el-checkbox-group v-else-if="item.type == 'checkbox'" v-model="formData.searchValue[item.prop]" :size="options.size || 'middle'" :placeholder="'请选择' + item.label" :clearable="item.clearable" :disabled="item.disabled" :style="{ width: item.width + 'px' }">
              <el-checkbox v-for="option in item.data" :key="option.value" :label="options.value" :disabled="options.disabled">{{ options.label }}</el-checkbox>
            </el-checkbox-group>
            <!-- 单选框 -->
            <el-radio-group v-else-if="item.type == 'radio'" v-model="formData.searchValue[item.prop]" :size="options.size || 'middle'" :placeholder="'请选择' + item.label" :clearable="item.clearable" :disabled="item.disabled" :style="{ width: item.width + 'px' }">
              <el-radio v-for="option in item.data" :key="option.label" :label="options.value" :size="options.size || 'middle'">{{ options.label }}</el-radio>
            </el-radio-group>
            <!-- 时间选择器 -->
            <el-date-picker v-else-if="item.type == 'date'" v-model="formData.searchValue[item.prop]" :size="options.size || 'middle'" :placeholder="'请选择' + item.label" :clearable="item.clearable" :disabled="item.disabled" :style="{ width: item.width + 'px' }" />
            <!-- 日期范围选择器 -->
            <el-date-picker
              v-else-if="item.type == 'daterange'"
              v-model="formData.searchValue[item.prop]"
              :type="item.type"
              :size="options.size || 'middle'"
              :placeholder="'请选择' + item.label"
              :clearable="item.clearable"
              :disabled="item.disabled"
              value-format="yyyy-MM-dd"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              :style="{ width: item.width }"
            />
            <!-- 文本域 -->
            <el-switch v-else-if="item.type == 'switch'" v-model="formData.searchValue[item.prop]" :placeholder="'请选择' + item.label" :clearable="item.clearable" :disabled="item.disabled" :style="{ width: item.width + 'px' }" />
            <el-input v-else-if="item.type == 'textarea'" v-model="formData.searchValue[item.prop]" :size="options.size || 'middle'" type="textarea" :placeholder="'请输入' + item.label" :clearable="item.clearable" :disabled="item.disabled" :style="{ width: item.width + 'px' }" />
          </el-form-item>
        </el-col>
        <slot></slot>
      </el-row>
    </el-form>
  </div>
</template>

<script>
import { baseSize } from '@/rem.js'
export default {
  props: {
    formData: {
      type: Object,
      default: () => {}
    },
    options: {
      type: Object,
      default: () => {}
    },
    width: {
      type: String,
      default: '520px'
    },
    title: {
      type: String,
      default: '新增'
    }
  },
  data() {
    return {}
  },
  computed: {
    labelWidth() {
      return `${this.options.labelWidth / baseSize}rem`
    }
  },
  methods: {
    handleSearchDialog() {
      this.$emit('searchDialog')
    },
    handelSelectFunc(item, value) {
      // eslint-disable-next-line no-prototype-builtins
      if (this.formData.hasOwnProperty('selectFun')) {
        this.formData.selectFun(item, value)
      }
    },
    onChange(prop, value) {
      this.formData.searchValue[prop] = value
      this.$emit('onChange', prop, value)
    },
    resetForm() {
      this.$refs.ruleFormRef.resetFields()
      for (const i in this.formData.searchValue) {
        this.formData.searchValue[i] = ''
      }
      // eslint-disable-next-line no-prototype-builtins
      if (this.formData.hasOwnProperty('queryBeforeCallback')) {
        this.formData.queryBeforeCallback()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.penk-form-container {
  .el-table-border {
    border: 1px #eee solid;
  }
  :deep(.el-dialog__body) {
    border-top: 1px solid #eee;
  }
  :deep(.el-form-item__error) {
    top: 80%;
    z-index: 99 !important;
    background-color: rgba(255, 0, 0, 0.5);
    font-weight: bold;
    color: #fff;
    padding: 4px;
    border-radius: 4px;
  }
}
</style>
