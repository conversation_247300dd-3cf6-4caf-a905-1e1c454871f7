import Cookies from 'js-cookie'

const TokenKey = 'satoken'
const SessionKey = 'satoken'
const EntTokenKey = 'entToken'

export function getToken() {
  return Cookies.get(TokenKey)
}

export function setToken(token) {
  return Cookies.set(To<PERSON><PERSON>ey, token)
}

export function getEntToken() {
  return Cookies.get(EntTokenKey)
}

export function removeEntToken() {
  return Cookies.remove(EntTokenKey)
}

export function setEntToken(token) {
  return Cookies.set(EntTokenKey, token)
}

export function removeToken() {
  return Cookies.remove(TokenKey)
}

export function removeSession() {
  return Cookies.remove(SessionKey)
}
