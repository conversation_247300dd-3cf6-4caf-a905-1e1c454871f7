
import { getEntFileInfoList } from '@/api/user'
import dict from '@/utils/dict'

let fileList = [
  // {
  //   certificateType: 'BUSINESS_LICENSE',
  //   fileName: '91460000MA5TPAAF25.jpg',
  //   filePath: 'sdqysp-pre/sdsp/b149916b36c24ababfca593e819394b6.jpg'
  // }
]
async function requestEntFileInfo() {
  try {
    const res = await getEntFileInfoList()
    if (res.isSuccess) {
      fileList = res.data
    }
  } catch (error) {
    console.log(error)
  }
}

// 获取营业执照
export async function getBusinessLicense() {
  if (!fileList.length) {
    await requestEntFileInfo()
  }
  return fileList.filter(i => i.certificateType === dict.certificateType.enum.BUSINESS_LICENSE)?.[0] || null
}

// 获取事业单位法人证书电子件
export async function getCertificateElectronics1() {
  if (!fileList.length) {
    await requestEntFileInfo()
  }
  return fileList.filter(i => i.certificateType === dict.certificateType.enum.INSTITUTION_LEGAL_PERSON_CERTIFICATE)?.[0] || null
}

// 获取民办非企业单位登记证书
export async function getCertificateElectronics2() {
  if (!fileList.length) {
    await requestEntFileInfo()
  }
  return fileList.filter(i => i.certificateType === dict.certificateType.enum.PRIVATE_NON_ENTERPRISE_REGISTRATION_CERTIFICATE)?.[0] || null
}
