module.exports = {
  title: '中国（海南）国际贸易单一窗口',
  fixedHeader: false, // 顶部导航是否固定
  sidebarLogo: false, // 是否显示左上角logo标题
  tagsView: true, // 否显示历史标签
  allowPermission: true, // 是否启用权限控制
  cityCode2Name: {
    yshk: '演示海口市',
    sy: '三亚市',
    cssy: '测试三亚',
    dz: '儋州市',
    wn: '万宁市',
    cm: '澄迈县',
    qh: '琼海市',
    wc: '文昌市',
    wzs: '五指山市',
    df: '东方市',
    da: '定安县',
    tc: '屯昌县',
    lg: '临高县',
    bslz: '白沙黎族自治县',
    cjlz: '昌江黎族自治县',
    ldlz: '乐东黎族自治县',
    lslz: '陵水黎族自治县',
    btlz: '保亭黎族自治县',
    qzlzmz: '琼中黎族苗族自治县',
    hk: '海口市'
  },
  pattern: {
    cardNoPattern:
          /([京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼]{1}(([A-HJ-Z]{1}[A-HJ-NP-Z0-9]{5})|([A-HJ-Z]{1}(([DF]{1}[A-HJ-NP-Z0-9]{1}[0-9]{4})|([0-9]{5}[DF]{1})))|([A-HJ-Z]{1}[A-D0-9]{1}[0-9]{3}警)))|([0-9]{6}使)|((([沪粤川云桂鄂陕蒙藏黑辽渝]{1}A)|鲁B|闽D|蒙E|蒙H)[0-9]{4}领)|(WJ[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼·•]{1}[0-9]{4}[TDSHBXJ0-9]{1})|([VKHBSLJNGCE]{1}[A-DJ-PR-TVY]{1}[0-9]{5})/,
    // numberLetter: '^[a-zA-Z0-9]{1,32}$', // 数字字母
    numberLetter: '^.{0,32}$', // 最多32位
    number: '^[0-9]*$', // 数字
    positive: '^([1-9]{1}[0-9]*(\\.[0-9]*)?$)|(^0(\\.[0-9]*[1-9])$)', // 大于0的数字
    phoneNumber: '^1[0123456789]\\d{9}$', // 手机号
    weight: '(^[1-9]{1}[0-9]{0,18}(\\.[0-9]{1,5})?$)|(^0(\\.[0-9]{0,4}[1-9])$)', // 大于0的数字19位5位
    weight5: '(^[1-9]{1}[0-9]{0,18}(\\.[0-9]{1,5})?$)|(^0(\\.[0-9]{0,5}[1-9])$)', // 大于0的数字19位5位
    numbers: '(^[0-9]{0,19}(\\.[0-9]{1,6})?$)|(^0(\\.[0-9]{0,6}[1-9])$)', // 大于等于0的数字19位6位
    number5:
      '(^\\d{1,13}(\\.\\d{1,5})$|^(\\-)\\d{1,12}(\\.\\d{1,5})$|^\\d{1,13}(\\.\\d{1,5})$|^([1-9])\\d{1,18}$|^(\\-)([1-9])\\d{1,17}$|^([1-9])\\d{1,18}$|^0$|^[1-9]$|^(\\-)[1-9]$)', // 19位5位，可负数
    number196:
      '(^\\d{1,12}(\\.\\d{1,6})$|^(\\-)\\d{1,11}(\\.\\d{1,6})$|^\\d{1,12}(\\.\\d{1,6})$|^([1-9])\\d{1,18}$|^(\\-)([1-9])\\d{1,17}$|^([1-9])\\d{1,18}$|^0$|^[1-9]$|^(\\-)[1-9]$)', // 19位6位，可负数
    number6: '(^\\d{1,12}(\\.\\d{1,6})$|^\\d{1,12}(\\.\\d{1,6})$|^([1-9])\\d{1,18}$|^([1-9])\\d{1,18}$|^0$|^[1-9]$)', // 19位6位，不可负数
    licensePlate:
      '^(([京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领][A-Z](([0-9]{5}[DF])|([DF]([A-HJ-NP-Z0-9])[0-9]{4})))|([京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领][A-Z][A-HJ-NP-Z0-9]{4}[A-HJ-NP-Z0-9挂学警港澳使领]))$' // 新能源车或者是普通油车车牌
  }

}
