<template>
  <div class="l-table edit-table">
    <el-form ref="tableForm" :model="tableForm" :rules="tableForm.tableFormRules" size="small">
      <!-- highlight-current-row  -->
      <el-table
        ref="pageTable"
        v-loading="dataLoading"
        class="no-ellipsis"
        tooltip-effect="light"
        :data="tableForm.tableColumnData"
        border
        :height="heightCasted"
        style="width: 100%"
        @selection-change="selectChange"
        @cell-dblclick="cellDblClick"
        @cell-click="cellClick"
      >
        <el-table-column v-if="checkbox" type="selection" width="50" align="center" />
        <el-table-column v-if="showIndex" type="index" width="50" label="序号" align="center" />
        <template v-for="item in columns">
          <el-table-column
            v-if="!item.hidden"
            :key="item.field"
            :prop="item.field"
            :class-name="item.className || ''"
            :label="item.label"
            :align="item.align || 'left'"
            :width="item.width"
            :min-width="item.minWidth || item.min_width"
            :fixed="item.fixed"
            :formatter="item.formatter"
            :show-overflow-tooltip="!item.isEdit"
          >
            <template #header>
              <div>
                <span v-if="getIsRequired(tableForm.tableFormRules[item.field])" class="required"></span>
                <span>{{ item.label }}</span>
                <el-tooltip v-if="item.tip" class="item" effect="dark" :content="item.tip" placement="top">
                  <i class="el-icon-info" style="cursor: pointer"></i>
                </el-tooltip>
              </div>
            </template>
            <template slot-scope="scope">
              <div v-if="item.type === 'switch'">
                <el-switch
                  v-model="scope.row[item.field]"
                  size="small"
                  :active-value="item.activeValue"
                  :inactive-value="item.inactiveValue"
                >
                </el-switch>
              </div>
              <div v-else-if="item.type === 'image'">
                <el-image
                  style="width: 60px; height: 60px"
                  :src="scope.row[item.field]"
                  :preview-src-list="[scope.row[item.field]]"
                >
                </el-image>
              </div>
              <div v-else-if="item.isEdit">
                <el-form-item
                  v-if="item.type === 'input'"
                  :class="getFormItemClass(tableForm.tableFormRules[item.field])"
                  style="margin-bottom: 0 !important"
                  :prop="'tableColumnData.' + scope.$index + '.' + item.field"
                  :rules="tableForm.tableFormRules[item.field]"
                >
                  <el-input
                    ref="inputRef"
                    v-model="scope.row[item.field]"
                    size="small"
                    :placeholder="'请输入' + item.label"
                    autofocus
                    :disabled="item.disabled || scope.row['disabled']"
                    @blur="inputBlur(scope)"
                    @input="handleInput(scope.row[item.field], scope)"
                    @change="handleChange(scope.row[item.field], scope, item.field)"
                  >
                    <template
                      v-if="
                        (item.append !== '' && item.append !== undefined && item.append !== null) ||
                        (item.appendFormatter && item.appendFormatter(scope.row))
                      "
                      #append
                    >
                      {{ item.appendFormatter ? item.appendFormatter(scope.row) : item.append }}
                    </template>
                  </el-input>
                </el-form-item>
                <el-form-item
                  v-else-if="item.type === 'select'"
                  style="margin-bottom: 0 !important"
                  :prop="'tableColumnData.' + scope.$index + '.' + item.field"
                  :rules="tableForm.tableFormRules[item.field]"
                >
                  <pageSelect
                    :value="scope.row[item.field]"
                    :list="item.options"
                    :dict-type="item.dictType"
                    :fetch-type="item.fetchType"
                    :placeholder="'请选择' + item.label"
                    :clearable="item.clearable"
                    :disabled="item.disabled"
                    :style="{ width: item.width + 'px' }"
                    @onchange="(val) => onChange(item, val, scope.$index, scope.row[item.field], scope, item.field)"
                  />
                </el-form-item>
              </div>
              <div v-else-if="item.isLink">
                <el-link type="primary" style="display: inline" @click="handleLink(scope)">{{
                  scope.row[item.field] || '请选择' + item.label
                }}</el-link>
                <i
                  v-if="item.canDelete && scope.row[item.field]"
                  class="el-icon-error"
                  style="margin-left: 6px; cursor: pointer; color: #666"
                  @click="handleRemove(scope)"
                ></i>
              </div>
              <div v-else-if="item.formatter" style="overflow: hidden; white-space: nowrap; text-overflow: ellipsis">
                {{ item.formatter(scope.row[scope.column.property]) }}
              </div>
              <div v-else style="overflow: hidden; white-space: nowrap; text-overflow: ellipsis">
                {{ scope.row[item.field] }}
              </div>
            </template>
          </el-table-column>
        </template>
        <slot name="event"></slot>
      </el-table>
    </el-form>
    <div v-if="showPagination" class="l-pages">
      <!-- 分页 -->
      <el-pagination
        :current-page="pages.pageNo"
        :page-size.sync="pages.pageSize"
        :page-sizes="pageSizes"
        :layout="layout"
        :total="total"
        @size-change="sizeChange"
        @current-change="currentChange"
      />
    </div>
  </div>
</template>

<script>
import { getListApi, deleteListApi } from '@/api/account/index'
import { isEmpty } from '@/utils/account/common'
import { uniqueId } from '@/utils/common.js'

export default {
  props: {
    showIndex: {
      type: Boolean,
      default: false
    },
    canOperate: {
      type: Boolean,
      default: true
    },
    showPagination: {
      type: Boolean,
      default: true
    },
    state: {
      type: Object,
      default() {
        return {
          rowIndex: -1,
          columnIndex: -1
        }
      }
    },
    pages: {
      type: Object,
      default() {
        return {
          pageNo: 1,
          pageSize: 10
        }
      }
    },
    tableData: {
      require: false,
      type: Array,
      default: () => {
        return []
      }
    },
    tableFormRules: {
      require: false,
      type: Object,
      default: () => {
        return {}
      }
    },
    layout: {
      type: String,
      default: 'total, sizes, prev, pager, next, jumper'
    },
    columns: {
      type: Array,
      default: () => {
        return []
      }
    },
    height: {
      type: [Number, String],
      default: 200
    },
    pageSizes: {
      type: Array,
      default() {
        return [10, 20, 30, 50]
      }
    },
    url: {
      type: String,
      default: ''
    },
    searchParams: {
      type: Object,
      default() {
        return {}
      }
    },
    checkbox: {
      type: Boolean,
      default: true
    }
  },

  data() {
    return {
      total: 0,
      dataLoading: false,
      selections: [],
      rowIndex: -1,
      tableColumnData: [] // 表格数据
    }
  },
  computed: {
    tableForm() {
      const obj = {
        tableColumnData: this.tableColumnData,
        tableFormRules: this.tableFormRules
      }
      return obj
    },
    heightCasted() {
      let val = ''
      if (this.height === 'auto') {
        val = undefined
      } else {
        val = this.height
      }
      return val
    }
  },
  watch: {
    'state.rowIndex': {
      handler(newCond, oldCond) {
        this.rowIndex = newCond
      },
      immediate: true,
      deep: true
    },
    tableData: {
      handler(newVal, oldVal) {
        if (!isEmpty(newVal)) {
          this.tableColumnData = newVal
          this.tableForm.tableColumnData = newVal
        }
      },
      immediate: true,
      deep: true
    }
  },
  mounted() {
    // this.getTableList()
  },
  methods: {
    getIsRequired(rules) {
      rules = rules || []
      const requiredArr = rules.filter((i) => i.required)
      let bool = false
      if (requiredArr.length > 0) {
        bool = true
      } else {
        bool = false
      }
      return bool
    },
    getFormItemClass(rules) {
      rules = rules || []
      const requiredArr = rules.filter((i) => i.required)
      const classArr = []
      if (requiredArr.length > 0) {
        classArr.push('is-required')
      } else {
        classArr.push('no-required')
      }
      return classArr
    },
    handleLink(scoped) {
      this.$emit('handleLink', scoped)
    },
    handleRemove(scoped) {
      this.$emit('handleRemove', scoped)
    },
    // 确定按钮
    confirm(callback) {
      console.log(this.tableForm.tableColumnData)

      this.$refs['tableForm'].validate((valid) => {
        callback(valid)
      })
    },
    onChange(item, value, index, value2, scope, field) {
      // 不要数组下标赋值，会失去响应式
      // this.tableColumnData[index][item.field] = value
      // this.tableForm.tableColumnData[index][item.field] = value

      // 使用map赋值，会保持响应式
      this.tableColumnData = this.tableColumnData.map((citem, i) => {
        if (i === index) {
          citem[item.field] = value
        }
        return citem
      })

      this.tableForm.tableColumnData = this.tableColumnData.map((citem, i) => {
        if (i === index) {
          citem[item.field] = value
        }
        return citem
      })

      if (item.field === 'accountNoIn') {
        if (item.options) {
          const option = item.options.find((option) => option.value === value)
          this.tableForm.tableColumnData[index].goodsType = option.type
        }
      }
      this.$forceUpdate()
      this.$emit('handleChange',  value2, scope, field)
    },
    handleDelAll() {
      if (this.selections.length === 0) {
        this.$message.warning('请先选择需要删除的数据')
        return
      }
      this.$confirm('是否确定删除该数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const idsArray = this.selections.map((selection) => selection.blId).join(',')
        deleteListApi(this.url, idsArray).then((res) => {
          if (res.code === 200) {
            this.$message.success('删除成功')
            this.getTableList()
          } else {
            this.$message.error(res.msg)
          }
        })
      })
    },
    // getTableList() {
    //   this.dataLoading = true
    //   getListApi(this.url + '/list', this.searchParams).then((res) => {
    //     this.tableColumnData = res.data.list
    //     this.dataLoading = false
    //   })
    // },
    getTableList() {
      return new Promise((resolve, reject) => {
        this.dataLoading = true
        getListApi(this.url + '/list', Object.assign(this.searchParams, this.pages))
          .then((res) => {
            const list = res.data.list.map((item) => ({
              ...item,
              id: item.id || uniqueId(),
              disabled: item.disabled === undefined ? !this.canOperate : item.disabled
            })) // .map(item=>({...item, isFocusProduct: item.isFocusProduct || '0'}))
            this.tableColumnData = list
            this.tableForm.tableColumnData = list
            this.total = res.data.total
            this.dataLoading = false
            resolve(list) // 异步请求成功，解析Promise
          })
          .catch((error) => {
            this.dataLoading = false
            reject(error) // 异步请求失败，拒绝Promise
          })
      })
    },
    pageTableClear() {
      this.$refs.pageTable.clearSelection()
    },
    cellDblClick(row, column, cell, event) {
      this.$emit('cellDblClick', row, column, cell, event)
    },
    cellClick(row, column, cell, event) {
      this.$emit('cellClick', { row, column, cell, event })
    },
    handleChange(value, scope, field) {
      this.$emit('handleChange', value, scope, field)
    },
    handleInput(value, scope) {
      this.$emit('handleInput', value, scope)
    },
    inputBlur(scoped) {
      this.rowIndex = 0
      this.columnIndex = 0
    },
    selectChange(selection) {
      this.selections = selection
      this.$emit('selectChange', selection)
    },
    fieldChange(row, option) {
      if (option[row]) {
        return option[row]
      }
    },
    sizeChange(item) {
      this.pages.pageSize = item
      this.getTableList()
      // this.emit('getDataList')
    },
    currentChange(item) {
      this.pages.pageNo = item
      this.getTableList()
      // this.emit('getDataList')
    },

    editInputBlur() {}
  }
}
</script>

<style scoped lang="scss">
.l-table {
  // height: calc(100vh - 420px);
  padding-bottom: 10px;
  .el-table {
    // height: calc(100% - 42px) !important;
  }

  ::v-deep .el-form-item.no-required .el-input__inner {
    background: #ffffff !important;
  }
  ::v-deep .el-form-item.is-required .el-input__inner {
    background: #1890ff0f !important;
  }

  .l-pages {
    margin-top: 10px;
  }

  ::v-deep .el-table tbody td {
    padding: 18px 0;
  }
}
:deep().no-ellipsis .cell {
  white-space: normal !important;
  overflow: visible;
  text-overflow: inherit;
}

.required:before {
  content: '*';
  color: #f56c6c;
  margin-right: 4px;
}
</style>
