import axios from 'axios'
import { Message } from 'element-ui'
import store from '@/store'
import { getToken } from '@/utils/auth'
import dayjs from 'dayjs'
import router from '@/router'
import { getEntUrl } from './method'

const addApplyCompanyCodeUrl = [
  'prcsCommon/goodsPage/list',
  'goods/addOrUpdate',
  'prcsCommon/selectSingleList',
  'prcsCommon/queryTransferDecNum',
  'prcsCommon/transferDecPage/list',
  'accountInvt/transferDecAccountInvt',
  'prcsCommon/pagePrcsProduct/list',
  'goods/list',
  'prcsCommon/select',
  'prcsCommon/materialList',
  'prcsCommon/invtList',
  'goods/addOrUpdate',
  'prcsCommon/pagePrcsProduct/list',
  'prcsCommon/pagePrcsDt/list',
  'prcsDt/queryMaterialByProductList',
  'accountInvt/pageRltInvtByPassport/list',
  'accountInvtProduct/pageRltInvtProduct/list',
  'prcsCommon/pagePrcsProduct/list',
  'logistics/listCommonVehicleInfo',
  'logistics/listTransportCommonInfo',
  'sasStockBsc/pageRltStock/list',
  'sasStockProduct/pageRltStockProduct/list',
  'outInAcountInvt/queryTransferNum',
  'accountInvt/pageRollOutInvt/list',
  'accountInvt/transferInAccountInvt',
  'pageBwsDt/list',
  'pagePrcsDtSimplify/list',
  'decAccOrConCdList/list',
  'summaryPrcsDtPage/list',
  'overviewBwsDtList',
  'overviewPrcsDtList',
  'accountZchwrd/listRdCompany',
  'accountZchwrd/availablePurchasePage'
]
const addApplyCompanySccdUrl = [
  'accountZchwrdRecord/purchasePage',
  'logistics/listCommonVehicleInfo',
  'logistics/listTransportCommonInfo',
  'accountZchwrd/listRdCompany',
  'accountZchwrd/availablePurchasePage'
]

// create an axios instance
const service = axios.create({
  baseURL: process.env.VUE_APP_BASE_API, // url = base url + request url
  // withCredentials: true, // send cookies when cross-domain requests
  timeout: 30000 // request timeout
})

const errorHandler = (error) => {
  if (error.response) {
    if (error.response.status === 401) {
      Message({
        message: 'Authorization verification failed',
        type: 'error',
        duration: 5 * 1000
      })
      setTimeout(() => {
        store.dispatch('user/resetToken').then(() => {
          // 使用 router 实例进行导航
          router.push('/login')
        })
      }, 3000)
    } else if (error.response.status === 302) {
      console.log(error.response)
    }
  }
  return Promise.reject(error)
}

// request interceptor
service.interceptors.request.use((config) => {
  // do something before request is sent

  const applyCompany = store.state.user.applyCompany || {}
  const url = config.url
  config.params = config.params || {}
  config.data = config.data || {}
  if (applyCompany.applyCompanyCode) {
    if (addApplyCompanyCodeUrl.some((item) => url.includes(item))) {
      if (config.method.toLowerCase() === 'get') {
        config.params = config.params || {}
        config.params['applyCompanyCode'] = applyCompany.applyCompanyCode
      } else {
        config.data = config.data || {}
        config.data['applyCompanyCode'] = applyCompany.applyCompanyCode
      }
    }

    if (addApplyCompanySccdUrl.some((item) => url.includes(item))) {
      if (config.method.toLowerCase() === 'get') {
        config.params['applyCompanySccd'] = applyCompany.applyCompanySccd
      } else {
        config.data['applyCompanySccd'] = applyCompany.applyCompanySccd
      }
    }
  }

  if (store.getters.token) {
    // let each request carry token
    // please modify it according to the actual situation
    config.headers['Authorization'] = getToken()
  }
  return config
}, errorHandler)

// response interceptor
service.interceptors.response.use(
  /**
   * If you want to get http information such as headers or status
   * Please return  response => response
   */

  /**
   * Determine the request status by custom code
   * Here is just an example
   * You can also judge the status by HTTP Status Code
   */
  (response) => {
    const res = response.data
    if (`${res.code}` === '10086' || `${res.code}` === '401') {
      Message({
        message: res.message || '未登录或登录已过期',
        type: 'error',
        duration: 5 * 1000
      })
      if (!window.location.origin.includes('localhost')) {
        const callbackUrl = getEntUrl()
        setTimeout(() => {
          location.href = callbackUrl
        }, 1000)
      }
      // setTimeout(() => {
      // store.dispatch('user/resetToken', {config: response.config}).then(() => {
      //   if (!window.location.origin.includes('localhost')) {
      //     location.href = 'https://test-psp.singlewindow.hn.cn/yqyc-ent-server/login/sso'
      //   }
      //   // router.push('/login')
      // })
      // }, 1000)
      return Promise.reject(new Error(res.message || 'Error'))
    } else if (response.config.responseType === 'blob') {
      // 导出
      const blob = new Blob([response.data], { type: 'application/vnd.ms-excel;charset=utf-8' })
      const downloadElement = document.createElement('a')
      const href = window.URL.createObjectURL(blob) // 创建下载的连接
      downloadElement.href = href
      downloadElement.download = decodeURI(response.headers['content-disposition'].split('=')[1]) // 处理文件名乱问题， 下载后文件
      document.body.appendChild(downloadElement)
      downloadElement.click() // 点击下载
      document.body.removeChild(downloadElement) // 下载完成移除元素
      window.URL.revokeObjectURL(href) // 释放掉blob对象
      return res
    } else if (response.headers['content-type'] === 'application/octet-stream') {
      const fileName = `report-${dayjs().format('YYYYMMDDHHmmss')}.txt`
      const downloadUrl = window.URL.createObjectURL(new Blob([res]))
      const link = document.createElement('a')
      link.href = downloadUrl
      link.setAttribute('download', fileName)
      document.body.appendChild(link)
      link.click()
      link.remove()
      return {
        status: 0
      }
    } else {
      return res
    }
  },
  errorHandler
)

export default service
