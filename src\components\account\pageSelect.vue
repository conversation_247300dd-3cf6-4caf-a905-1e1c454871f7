<template>
  <el-select
    v-model="status"
    :style="{width:width}"
    filterable
    :disabled="disabled"
    :placeholder="placeholder"
    clearable
    @change="onchange"
    @filter-method="filterMethod"
  >
    <el-option v-for="(item, index) in optionsList" :key="item.value + index" :label="item.label" :value="item.value" />
  </el-select>
</template>
<script>
import { getSingleDict } from '@/utils/account/common'
export default {
  name: 'PageSelect',
  props: {
    placeholder: {
      type: String,
      default: ''
    },
    /**
     * 是否需要网络请求
     * */
    fetchType: {
      type: Boolean,
      default: true
    },
    value: {
      type: [String, Number],
      default: ''
    },
    width: {
      type: String,
      default: ''
    },
    disabled: {
      type: Boolean,
      default: false
    },
    list: {
      type: Array,
      default: () => {
        return []
      }
    },
    dictType: {
      type: String,
      default: ''
    },
    // 选项包含的值
    includeOptionValues: {
      type: Array,
      default: _ => ([])
    },
    // 选项排除的值
    excludeOptionValues: {
      type: Array,
      default: _ => ([])
    }
  },
  data() {
    return {
      status: '',
      optionsList: [],
      filteredOptionsList: [],
      newCode: ''
    }
  },
  watch: {
    // 监听value数据变化
    value: {
      handler(newValue, oldValue) {
        this.status = newValue + ''
      },
      deep: true,
      immediate: true
    },
    list: {
      deep: true,
      immediate: true,
      handler() {
        if (!this.fetchType) {
          this.optionsList = this.list
          this.filteredOptionsList = this.list
        }
      }
    }
  },
  created() {
    if (!this.fetchType) {
      this.optionsList = this.list
      this.filteredOptionsList = this.list
    } else {
      this.fetchEnumListData()
    }
  },
  methods: {
    async fetchEnumListData() {
      let allOptions = await getSingleDict(this.dictType)
      if (this.includeOptionValues?.length) {
        allOptions = allOptions.filter(i => this.includeOptionValues.includes(i.value))
      } else if (this.excludeOptionValues?.length) {
        allOptions = allOptions.filter(i => !this.excludeOptionValues.includes(i.value))
      }
      this.optionsList = allOptions
      this.filteredOptionsList = allOptions
    },

    onchange(value) {
      this.$emit('update:value', value)
      this.$emit('onchange', value)
    },
    filterMethod(query) {
      // 根据输入值过滤选项列表
      if (query) {
        this.filteredOptionsList = this.optionsList.filter(item => {
          return item.label.toLowerCase().includes(query.toLowerCase())
        })
      } else {
        this.filteredOptionsList = this.optionsList
      }
    }
  }
}
</script>
