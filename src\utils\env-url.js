let hasExecuteCheck= false // 是否执行过
let isGovNet = true // 是否是政府内网

// 检测环境
function checkGovNet() {
  return new Promise((resolve, reject) => {
    fetch(`https://test-psp.singlewindow.hn.cn/sdqysp-server/appBill/detail/123`)
      .then((res) => {
        console.log('调用ding接口成功', res)
        if (res.status === 200) {
          isGovNet = false
        } else {
          isGovNet = true
        }
        resolve(isGovNet)
      })
      .catch((e) => {
        console.log(e)
        console.log('调用ding接口失败', e)
        isGovNet = true
        resolve(isGovNet)
      })
  })
}

function psp2ding(url) {
  let ret = url
  if (process.env.NODE_ENV === 'production') {
    ret = url.replaceAll('https://psp.singlewindow.hn.cn', 'https://app-ding.digitalhainan.com.cn:10793')
  }
  if (process.env.NODE_ENV === 'development') {
    ret = url.replaceAll('https://test-psp.singlewindow.hn.cn', 'https://app-ding.digitalhainan.com.cn:10798')
  }
  console.log('psp2ding：', ret)
  return ret
}

function ding2psp(url) {
  let ret = url
  if (process.env.NODE_ENV === 'production') {
    ret = url.replaceAll('https://app-ding.digitalhainan.com.cn:10793', 'https://psp.singlewindow.hn.cn')
  }
  if (process.env.NODE_ENV === 'development') {
    ret = url.replaceAll('https://app-ding.digitalhainan.com.cn:10798', 'https://test-psp.singlewindow.hn.cn')
  }
  console.log('ding2psp：', ret)
  return ret
}

// 获取环境的url
export async function getEnvUrl(url) {
  if (!hasExecuteCheck) {
    const bool = await checkGovNet()
    console.log('首次检测是否是政府内网：', isGovNet, bool)
    hasExecuteCheck = true
  }
  //
  console.log('是否是政府内网：', isGovNet)
  console.log('url：', url)
  if (isGovNet) {
    return psp2ding(url)
  } else {
    return ding2psp(url)
  }
}

// 获取当前环境
export const getNodeEnv = () => {
  const env = process.env.NODE_ENV
  let ret = 'local'
  if (env === 'development' && window.location.host.includes('test-psp')) {
    ret = 'dev'
  } else if (env === 'production') {
    ret = 'prod'
  }
  return ret
}

// 获取基础url，区分生产私域部署
export const getBaseUrl = () => {
  const path = location.pathname === '/zc-ent/' ? '/sp-ent-server' : '/sdqysp-server-ent'
  return location.origin + path
}
