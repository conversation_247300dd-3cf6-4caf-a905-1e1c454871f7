/**
 * 保留n位小数点
 * @param {*} num
 * @param {*} decimal 精度
 * @returns String
 */
export function toDecimal(num, decimal) {
  num = num.toString()
  const index = num.indexOf('.')
  if (index !== -1) {
    num = num.substring(0, decimal + index + 1)
  } else {
    num = num.substring(0)
  }
  return parseFloat(num).toFixed(decimal)
}

/**
 * 生成唯一的ID。如果给定 “前缀”，则前缀将附加到该ID。
 * @param {string} [prefix=''] ID的前缀值
 * @returns {string} 返回唯一的ID
 */
const idCounter = {}
export function uniqueId(prefix = '$lodash$') {
  if (!idCounter[prefix]) {
    idCounter[prefix] = 0
  }

  const id = ++idCounter[prefix]
  if (prefix === '$lodash$') {
    return `${id}`
  }

  return `${prefix}${id}`
}

/**
 * 生成唯一uuid
 * @param
 * @return {string}
 */
export function generateUUID() {
  let d = new Date().getTime()
  const uuid = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = (d + Math.random() * 16) % 16 | 0
    d = Math.floor(d / 16)
    return (c === 'x' ? r : (r & 0x3 | 0x8)).toString(16)
  })
  return uuid.replace(/-/g, '')
}
