<template>
  <div class="base-page">
    <iframe width="100%" height="100%" :src="src" frameborder="0"></iframe>
  </div>
</template>
<script>
export default {
  name: 'IframePage',
  data() {
    return {
      src: ''
    }
  },
  mounted() {
    const hash = window.location.hash
    this.src = `${window.location.origin}/hnzf/hnzfWeb/${hash}&BType=equipment`
  }
}
</script>
<style lang="scss" scoped>
  .base-page{
    padding: 0;
    height: calc(100vh - 50px);
  }
</style>
