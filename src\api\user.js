import request from '@/utils/request'
import { getBaseUrl } from '@/utils/env-url'

// const baseUrl = location.origin // 'https://test-psp.singlewindow.hn.cn' // process.env.NODE_ENV === 'development' ? 'https://test-psp.singlewindow.hn.cn' : 'https://psp.singlewindow.hn.cn'

export function login(data) {
  return request({
    url: '/login/doLogin',
    method: 'post',
    data
  })
}

export function getUserInfo() {
  return request({
    url: '/user/getCurrUserInfo',
    method: 'post',
    data: {
      ticketSNO: localStorage.getItem('ticketSNO')
    }
  })
}

export function submitFile(data) {
  return request({
    url: getBaseUrl() + '/appBillProves/submit',
    method: 'post',
    data
  })
}

export function getHybUserInfo(data) {
  return request({
    url: getBaseUrl() + '/workflow/ent/user/getCurrUser',
    method: 'post',
    data
  })
}

export function detail(data) {
  return request({
    url: getBaseUrl() + '/workflow/ent/apply/detail',
    method: 'get',
    params: { billId: data.billId }
  })
}
export function isLianheVerify(data) {
  return request({
    url: getBaseUrl() + '/workflow/ent/apply/isLianheVerify',
    method: 'get',
    params: data
  })
}

export function loginHyb(data) {
  return request({
    url: `/login/hyb`,
    method: 'get',
    params: { ticketSNO: localStorage.getItem('ticketSNO') }
  })
}

export function submit(data) {
  return request({
    url: getBaseUrl() + '/workflow/ent/apply/submit',
    method: 'post',
    data
  })
}

export function logout() {
  return request({
    url: '/login/logout',
    method: 'get',
    params: {}
  })
}

// 获取企业用户证件文件
export function getEntFileInfoList() {
  return request({
    url: getBaseUrl() + '/workflow/ent/user/getEntFileInfoList',
    method: 'post',
    params: {}
  })
}

// 获取企业用户证件文件，通过信用代码
export function getEntFileBySccd(data) {
  return request({
    url: getBaseUrl() + '/workflow/ent/user/getEntFileInfoListBySccd',
    data,
    method: 'post'
  })
}
