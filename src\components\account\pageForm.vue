<template>
  <div class="penk-form-container editForm">
    <el-form
      ref="ruleFormRef"
      :inline="true"
      :size="options.size || 'middle'"
      :disabled="formData.disabled.formDisabled"
      :label-width="options.labelWidth + 'px'"
      :model="formData.formValue"
      :rules="formData.rule"
    >
      <el-row :gutter="16">
        <el-col v-for="item in options.column" :key="item.prop" :span="item.col">
          <el-form-item
            :label="item.label + ':'"
            :style="{ display: item.hidden == true ? 'none' : '' }"
            :prop="item.prop"
          >
            <template v-if="item.tip" #label>
              <div style="display: inline-block;">
                <span>{{ item.label }}</span>
                <el-tooltip class="item" effect="dark" :content="item.tip" placement="top">
                  <i class="el-icon-info"></i>
                </el-tooltip>
                <span>:</span>
              </div>
            </template>

            <!-- 输入框 -->
            <template v-if="item.type == 'input'">
              <div class="input-icon-wrap">
                <el-input
                  v-model="formData.formValue[item.prop]"
                  :size="options.size || 'middle'"
                  :placeholder="'请输入'+item.label"
                  :clearable="item.clearable"
                  :disabled="item.disabled"
                  :type="item.inputType"
                  :row="item.row"
                  :style="{ width: item.width + 'px' }"
                  @input="handleInput($event,item)"
                  @change="handleChange($event, item.prop)"
                >
                  <el-button
                    v-if="item.addbtn"
                    slot="append"
                    type="primary"
                    size="small"
                    icon="el-icon-circle-plus-outline"
                    :disabled="item.disabled"
                    @click="handleSearchDialog(item.prop)"
                  >查询</el-button>
                </el-input>
                <el-tooltip v-if="item.tipContent" effect="dark" :content="item.tipContent" placement="top">
                  <i class="el-icon-info" style="cursor: pointer"></i>
                </el-tooltip>
              </div>
            </template>
            <!-- 下拉框 -->
            <pageSelect
              v-else-if="item.type == 'select'"
              :value="formData.formValue[item.prop] ? formData.formValue[item.prop] : ''"
              :list="item.options"
              :dict-type="item.dictType"
              :fetch-type="item.fetchType"
              :placeholder="'请选择' + item.label"
              :clearable="item.clearable"
              :disabled="item.disabled"
              :style="{ width: item.width + 'px' }"
              @onchange="(val) => onChange(item, val)"
            />
            <!-- <el-select v-else-if="item.type == 'select'" v-model="formData.formValue[item.prop]" :placeholder="'请选择'+item.label" :clearable="item.clearable" :disabled="item.disabled" :style="{ width: item.width + 'px' }" @change="handelSelectFunc(item,formData.formValue[item.prop])">
              <el-option v-for="option in item.options" :key="option.value" :label="option.label" :value="option.value" :disabled="option.disabled" />
            </el-select> -->
            <!-- 多选框 -->
            <el-checkbox-group
              v-else-if="item.type == 'checkbox'"
              v-model="formData.formValue[item.prop]"
              :size="options.size || 'middle'"
              :placeholder="item.placeholder"
              :clearable="item.clearable"
              :disabled="item.disabled"
              :style="{ width: item.width + 'px' }"
            >
              <el-checkbox
                v-for="option in item.data"
                :key="option.value"
                :label="option.value"
                :disabled="option.disabled"
              >{{ option.label }}</el-checkbox>
            </el-checkbox-group>
            <!-- 单选框 -->
            <el-radio-group
              v-else-if="item.type == 'radio'"
              v-model="formData.formValue[item.prop]"
              :size="options.size || 'middle'"
              :placeholder="item.placeholder"
              :clearable="item.clearable"
              :disabled="item.disabled"
              :style="{ width: item.width + 'px' }"
            >
              <el-radio
                v-for="option in item.data"
                :key="option.label"
                :label="option.value"
                :size="option.size || 'middle'"
              >{{ option.label }}</el-radio>
            </el-radio-group>
            <!-- 时间选择器 -->
            <el-date-picker
              v-else-if="item.type == 'date'"
              v-model="formData.formValue[item.prop]"
              :size="options.size || 'middle'"
              :placeholder="item.placeholder"
              :clearable="item.clearable"
              :disabled="item.disabled"
              :style="{ width: item.width + 'px' }"
            />
            <!-- 文本域 -->
            <el-switch
              v-else-if="item.type == 'switch'"
              v-model="formData.formValue[item.prop]"
              :placeholder="item.placeholder"
              :clearable="item.clearable"
              :disabled="item.disabled"
              :style="{ width: item.width + 'px' }"
            />
            <el-input
              v-else-if="item.type == 'textarea'"
              v-model="formData.formValue[item.prop]"
              :size="options.size || 'middle'"
              type="textarea"
              :placeholder="item.placeholder"
              :clearable="item.clearable"
              :disabled="item.disabled"
              :style="{ width: item.width + 'px' }"
            />
          </el-form-item>
        </el-col>
        <slot></slot>
      </el-row>
    </el-form>
  </div>
</template>
<script>
export default {
  props: {
    formData: {
      type: Object,
      default: () => {}
    },
    options: {
      type: Object,
      default: () => {}
    },
    width: {
      type: String,
      default: '520px'
    },
    title: {
      type: String,
      default: '新增'
    }
  },
  data() {
    return {}
  },
  computed: {
    labelWidth() {
      return `${this.options.labelWidth}px`
    }
  },
  methods: {
    handleChange(value, prop) {
      this.$emit('change', prop, value)
    },
    handleInput(value, item) {
      let ret = value
      if (item?.upperCase) {
        ret = ret.toUpperCase()
      }

      if (item?.zhSemi2en) {
        ret = ret.replaceAll('；', ';')
      }

      if (item?.trim) {
        ret = ret.trim()
      }

      this.formData.formValue[item.prop] = ret
    },
    handleValid() {
      return new Promise((resolve, reject) => {
        this.$refs['ruleFormRef'].validate((valid) => {
          if (valid) {
            resolve(valid)
          } else {
            reject(new Error('表单验证失败'))
          }
        })
      })
    },
    onChange(item, value) {
      this.formData.formValue[item.prop] = value
      this.$emit('onChange', item.prop, value, item)
      if (Object.prototype.hasOwnProperty.call(this.formData, 'selectFun')) {
        if (item.options) {
          const option = item.options.find((option) => option.value === value)
          this.formData.selectFun(item, option)
        } else {
          this.formData.selectFun(item, value)
        }
      }
    },
    handleSearchDialog(prop) {
      this.$emit('searchDialog', prop)
    },
    handelSelectFunc(item, value) {
      if (Object.prototype.hasOwnProperty.call(this.formData, 'selectFun')) {
        this.formData.selectFun(item, value)
      }
    },
    resetForm() {
      this.$refs.ruleFormRef.resetFields()
      for (const i in this.formData.formValue) {
        this.formData.formValue[i] = ''
      }
      if (Object.prototype.hasOwnProperty.call(this.formData, 'queryAddCallback')) {
        this.formData.queryAddCallback()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.penk-form-container {
.input-icon-wrap{
  display: flex;
  align-items: center;
}
  .el-table-border {
    border: 1px #eee solid;
  }
  :deep(.el-dialog__body) {
    border-top: 1px solid #eee;
  }
  :deep(.el-form-item__error) {
    top: 80%;
    z-index: 99 !important;
    background-color: rgba(255, 0, 0, 0.5);
    font-weight: bold;
    color: #fff;
    padding: 4px;
    border-radius: 4px;
  }

  ::v-deep .el-form-item .el-input__inner {
    background: #ffffff !important;
  }
  ::v-deep .el-form-item.is-required .el-input__inner {
    background: #1890ff0f !important;
  }
  ::v-deep .el-form-item .is-disabled .el-input__inner {
    background: #f5f7fa !important;
  }
}
</style>
