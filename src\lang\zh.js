export default {
  route: {
    home: '首页',
    errorPages: '错误页面',
    page401: '401',
    page404: '404',
    directRelease: '一线径予放行',
    entryArea: '进境入区',
    exitArea: '一线出境',
    areaPort: '区港联动',
    inAndOutApply: '进出卡口申请',
    inAndOutManage: '进出卡口管理',
    inAndOutDetail: '进出卡口详情',
    selfdeclaration: '企业自主声明创建',
    entrySelfdeclaration: '进境入区-企业自主声明创建',
    exitSelfdeclaration: '一线出境-企业自主声明创建',
    selfdeclarationManagement: '企业自主声明管理',
    entrySelfdeclarationManagement: '进境入区-企业自主声明管理',
    exitSelfdeclarationManagement: '一线出境-企业自主声明管理',
    selfdeclarationDetail: '企业自主声明详情',
    entrySelfdeclarationDetail: '进境入区-企业自主声明详情',
    exitSelfdeclarationDetail: '一线出境-企业自主声明详情',
    unilateralDeclaration: '二线进出区单侧申报',
    regionEnterManagement: '入区管理',
    entryApplication: '入区提货申请',
    enterManagement: '入区提货管理',
    regionEnterDetail: '入区发货详情',
    enterVoucherApplication: '入区卡口申请',
    enterVoucherManagement: '入区卡口管理',
    enterVoucherInformation: '入区卡口验放凭证信息',
    regionEnterprises: '出区管理',
    deliveryApplication: '出区发货申请',
    deliveryManagement: '出区发货管理',
    deliveryDetail: '出区发货详情',
    voucherApplication: '出区卡口申请',
    voucherManagement: '出区卡口管理',
    voucherInformation: '出区卡口验放凭证信息',
    regionalRelease: '企业通关服务数据管理',
    warehouseEntry: '进境入区-入库单创建',
    warehouseManagement: '进境入区-入库单',
    warehouseDetail: '进境入区-入库单详情',
    warehouseExit: '一线出境-出库单创建',
    exitManagement: '一线出境-出库单',
    exitDetail: '一线出境-出库单详情',
    regionalReleaseRecord: '企业通关服务备案',
    productRecord: '料件备案',
    productRecordSubmit: '料件成品备案',
    productRecordSearch: '料件成品备案管理',
    vehicleRecord: '车辆备案',
    vehicleRecordList: '车辆备案',
    vehicleRecordDetail: '车辆备案详情',
    productRecordAdd: '新增料件成品备案',
    productRecordDetail: '料件成品备案查询详情',
    productRecordEdit: '料件成品备案提交详情',
    enterpriseServiceRecordAndDeclaration: '企业服务备案及声明',
    enterpriseIdentityInformation: '企业身份资料',
    serviceRecord: '服务备案',
    customsServiceRecordManagement: '海关服务备案管理',
    enterpriseInfomation: '企业身份资料',
    serviceInfo: '服务介绍'
  },
  header: {
    titleRegional: '洋浦保税港区公共信息服务平台',
    titleService: '洋浦保税港区公共信息服务平台',
    logout: '退出'
  },
  navbar: {
    home: '首页',
    github: '项目地址',
    logOut: '退出登录',
    profile: '个人中心',
    theme: '换肤',
    size: '布局大小'
  },
  screenfull: {
    notWork: '当前浏览器暂不支持全屏'
  },
  switchSize: {
    default: '默认',
    medium: '中',
    small: '小',
    mini: '迷你',
    success: '修改大小成功'
  },
  tagsView: {
    newTag: '新标签页打开',
    refresh: '刷新',
    close: '关闭',
    closeOthers: '关闭其它',
    closeAll: '关闭所有',
    closeOperation: '关闭操作'
  },
  login: {
    title: '系统登录',
    logIn: '登录',
    username: '账号',
    password: '密码',
    any: '随便填',
    thirdparty: '第三方登录',
    thirdpartyTips: '本地不能模拟，请结合自己业务进行模拟！！！'
  },
  errorPage: {
    back: '返回',
    noauthTips: '暂无权限访问此页面',
    backHome: '返回首页',
    notFountTips: '找不到此页面',
    checkTips: '请检查访问地址是否正确，或点击下方按钮返回首页'
  },
  permission: {
    addRole: '新增角色',
    editPermission: '编辑权限',
    roles: '你的权限',
    switchRoles: '切换权限',
    delete: '删除',
    confirm: '确定',
    cancel: '取消'
  },
  guide: {
    description: '引导页对于一些第一次进入项目的人很有用，你可以简单介绍下项目的功能。本 Demo 是基于',
    button: '打开引导'
  },
  table: {
    dynamicTips1: '固定表头, 按照表头顺序排序',
    dynamicTips2: '不固定表头, 按照点击顺序排序',
    dragTips1: '默认顺序',
    dragTips2: '拖拽后顺序',
    title: '标题',
    importance: '重要性',
    type: '类型',
    remark: '点评',
    search: '搜索',
    add: '添加',
    export: '导出',
    reviewer: '审核人',
    id: '序号',
    date: '时间',
    author: '作者',
    readings: '阅读数',
    status: '状态',
    actions: '操作',
    edit: '编辑',
    publish: '发布',
    draft: '草稿',
    delete: '删除',
    cancel: '取 消',
    confirm: '确 定'
  },
  unilateralDeclaration: {
    voucher: '2-进出区收发货凭证',
    customsPassTime: '海关通过/过卡时间',
    systemNumber: '系统序号',
    declaredQuantity: '申报数量',
    goodsNetWeight: '货物净重',
    originCountry: '原产国(地区)',
    targetCountry: '最终目的国（地区）',
    goodsSourceType: '货物来源单证类型',
    goodsSourceNo: '货物来源单证编号',
    goodsGrossWeight: '货物毛重',
    voucherEntrNumber: '卡口验放凭证预录入编号',
    voucherNumber: '卡口验放凭证编号',
    voucherTypeCode: '卡口验放类型代码',
    accessSign: '进出标志',
    accessSignCode: '进出标志代码',
    acceptCarNumber: '承运车车牌号',
    carWeight: '车自重',
    ICCardNumber: 'IC卡号(电子车牌)',
    frameNumber: '车架号',
    frameWeight: '车架重',
    containerType: '集装箱箱型',
    containerWeight: '集装箱重',
    cutMode: '征免性质分类',
    entry_number: '入区发货申请预录入编号',
    entryNumber: '出区发货申请预录入编号',
    entryNumberPlace: '发货申请预录入编号',
    entryNumberDeclaration: '预录入编号',
    entryNumberDeclaration1: '企业自主声明预录入编号',
    applicationNumber: '出区发货申请单号',
    application_number: '入区发货申请单号',
    typeCode: '申报类型代号',
    companyCode: '企业编号',
    companyName: '企业名称',
    importExportSigns: '进出口标志',
    deliveryType: '提发货类型',
    orderCode: '销售订单编号',
    superviseWay: '监管方式',
    warehousingTime: '预计出库时间',
    warehousing_time: '预计入库时间',
    customsSigns: '报关标志',
    customsManagementCodePlaceholder: '海关经营单位编码',
    customsManagementNamePlaceholder: '经营单位名称',
    customsDeliveryCodePlaceholder: '海关收发货企业编号',
    customsDeliveryNamePlaceholder: '海关收发货企业名称',
    customsManagementCode: '关联报关单企业海关经营单位编码',
    customsManagementName: '关联报关单企业经营单位名称',
    customsDeliveryCode: '关联报关单企业海关收发货企业编号',
    customsDeliveryName: '关联报关单企业海关收发货企业名称',
    customsDeclarationNo: '关联区外报关单号',
    approvalNumber: '批准证编号',
    importExportPort: '进/出口岸',
    entryExitCustoms: '进出境关別',
    masterCustomCode: '主管关区代码',
    arrivingCountry: '启抵国(地区)',
    endCountry: '产终国(地区)',
    shippingType: '运输方式',
    remarks: '备注',
    statementNumber: '径予声明编号',
    jinerAccountNo: '金二账册号',
    materielCode: '物料编号',
    materielName: '物料名称',
    productSigns: '料件产品标志',
    materielSigns: '料件成品标志',
    hsCode: 'HS编码',
    hsName: 'HS商品名称',
    specificationsModels: '规格型号',
    brandType: '品牌类型',
    inOutNumber: '出库数量',
    calcUnit: '计量单位',
    unit: '法定计量单位',
    unit2: '法定第二计量单位',
    dutyMode: '征免方式',
    unit1Num: '第一法定数量',
    unit2Num: '第二法定数量',
    currency: '币制',
    price: '单价',
    account: '总价',
    state: '状态',
    applicationTime: '申请时间',
    receivingCompany: '收货企业名称',
    customsAdoptTime: '海关通过时间',
    vehicleTypeCode: '车辆绑定类型代码',
    relationPreCode: '关联预录入单证编号',
    relationCode: '关联单证编号',
    relationType: '关联单证类型',
    relationTypeCode: '关联单证类型代码',
    transportToolCode: '运输工具代码',
    transportToolName: '运输工具名称',
    voyageNumber: '航次',
    transportOrder: '提运单号',
    cargoFlow: '货物流向',
    grossWeight: '毛重',
    containerNumber: '集装箱号',
    goodsDesc: '货物说明',
    pleaseInput: '请输入',
    pleaseSelect: '请选择',
    containerNumberPlaceholder: '请输入集装箱号(多个用;隔开),不填写时默认为散货',
    goodsNumber: '件数',
    districtPortSign: '区港联动标记',
    startPlaceCode: '启运地场所代码',
    startAreaCode: '启运地关区代码',
    endPlaceCode: '指运地场所代码',
    endAreaCode: '指运地关区代码',
    statementTime: '声明时间',
    customsClearanceTime: '海关放行时间',
    direcVoucher: '径予凭证',
    recordNo: '备案序号',
    recordName: '备案名称',
    warehouseReceiptHandle: '入库单操作'
  },
  regionalRelease: {
    warehousePreNumber: '入库单预录入编号',
    exitPreNumber: '出库单预录入编号',
    purchaseOrder: '采购订单编号',
    warehouseNumber: '入库单编号',
    saleNumber: '销售订单编号',
    exitNo: '出库单编号',
    applicationCode: '申请类型代码',
    purchaseType: '采购类型',
    exitType: '出库类型',
    contractNo: '合同编号',
    contractAmount: '合同金额',
    supplierNo: '供应商编号',
    supplierName: '供应商名称',
    purchaseDate: '采购日期',
    entryWarehousingTime: '入库时间',
    exitWarehousingTime: '出库时间',
    approvalCardNumber: '批准证编号',
    versionNo: '版本号',
    finishedProductMark: '物件成品标志',
    entryNumber: '入库数量',
    exitNumber: '出库数量',
    warehouseCode: '货仓代码',
    positionCode: '仓位代码',
    productTypeCode: '物料类别',
    submitTime: '提交时间',
    listNo: '核注清单号',
    controlMark: '布控标记',
    productTypeDesc: '物料类别描述',
    goodsSourceType: '货物来源单证类型',
    goodsSourceNo: '货物来源单证编号',
    inventoryOrderNo: '核注清单项次号',
    remark: '备注'
  },
  productRecordSubmit: {
    preEntryNumber: '预录入编号',
    productNumber: '物料/产品编号',
    productName: '物料/产品名称',
    productSymbol: '料件成品标志',
    accountNumber: '金二账册号',
    recordSerialNumber: '备案序号',
    recordName: '备案名称',
    HSNumber: 'HS编码',
    HSCommodityName: 'HS商品名称',
    commodityType: '商品规格型号',
    submitTime: '提交时间',
    returnTime: '回执时间',
    unitConversionRate: '单位转换率',
    subsidiaryMarkingCode: '辅料标记代码',
    modifyMarkCode: '修改标记代码',
    enterpriseExecutionMarkCode: '企业执行标记代码',
    customsExecutionMarkCode: '海关执行标记代码',
    status: '状态',
    passTime: '海关通过时间',
    remarks: '备注'
  },
  vehicleRecord: {
    recordTime: '录入时间',
    numberOfCar: '承运车车牌号',
    containerNumber: '集装箱号',
    electronicLicense: 'C卡号(电子车牌)',
    vehicleWeight: '车自重',
    vehicleIdentifyNumber: '车架号',
    vehicleFrameWeight: '车架重',
    containerType: '集装箱箱型',
    containerWeigth: '集装箱重'
  },
  enterpriseInfomation: {
    unifiedSocialCreditCode: '统一社会信用代码',
    organizationCode: '组织机构代码（主体标识码）',
    chineseNameOfEnterprise: '企业中文名称',
    legalPerson: '法人（负责人）姓名',
    legalPersonCertificateType: '法人（负责人）证件类型',
    legalPersonCertificateNumber: '法人（负责人）证件号码',
    IDEffectiveDate: '身份证有效起始日期',
    IDvalidEndDate: '身份证有效结束日期',
    customsRegistrationCode: '海关注册编码',
    customsEnterprisesType: '海关企业类型',
    address: '联系地址',
    contract: '联系人',
    telephone: '电话号码'
  }
}
