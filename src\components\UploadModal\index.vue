<template>
  <el-dialog title="Excel上传" :visible.sync="uploadDialog">
    <a :href="uploadSrc" download>下载模板</a>
    <el-upload
      class="upload-box"
      drag
      action="https://jsonplaceholder.typicode.com/posts/"
      multiple
      :file-list="fileList"
      @on-success="uploadSuccess"
    >
      <i class="el-icon-upload"></i>
      <div class="el-upload__text">
        将文件拖到此处，或
        <em>点击上传</em>
      </div>
      <div slot="tip" class="el-upload__tip">只能上传jpg/png文件，且不超过500kb</div>
    </el-upload>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleUpload(false)">取 消</el-button>
      <el-button type="primary" @click="handleUpload(true)">确 定</el-button>
    </span>
  </el-dialog>
</template>
<script>
export default {
  props: {
    uploadDialog: {
      type: Boolean,
      default: false
    },
    uploadSrc: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      fileList: []
    }
  },
  methods: {
    handleUpload(isUpload) {
      this.$emit('isUploadHandle', isUpload)
    },
    uploadSuccess(response, file, fileList) {
      console.log(response, file, fileList)
    }
  }
}
</script>
<style lang="scss" scoped>
.upload-box {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
</style>
