import request from '@/utils/request'

import { getBaseUrl } from '@/utils/env-url'

// 删除申请单
export function entApplyDelete(data) {
  return request({
    url: getBaseUrl() + '/workflow/ent/apply/delete',
    data,
    method: 'post'
  })
}

// 根据字典类型获取字典数据
export function getDictList(data) {
  // {"type":"yp_unit"}  计量单位
  return request({
    url: getBaseUrl() + '/common/dict/getDictList',
    data,
    method: 'post'
  })
}

// 获取申请区域列表
export function getRegionList(data) {
  // {"billType": "zchwrd"} 自产货物认定
  return request({
    url: getBaseUrl() + '/workflow/ent/apply/regionlist',
    data,
    method: 'post'
  })
}

/**
 * 获取企业信息
 * @param {*} data
 * @returns
 */
export function getCompanyInfoBySccd(data) {
  // {"billType": "zchwrd"} 自产货物认定
  return request({
    url: getBaseUrl() + '/workflow/ent/apply/getCompanyInfoBySccd',
    data,
    method: 'post'
  })
}

// 生成RD唯一码
export function getUniqueRDcode(data) {
  return request({
    url: getBaseUrl() + '/workflow/ent/apply/getUniqueRDcode',
    data,
    method: 'post'
  })
}

// 查询审核通过单据列表
export function getPassAppBillList(data) {
  return request({
    url: getBaseUrl() + '/workflow/ent/apply/selectPassAppBillList',
    data,
    method: 'post'
  })
}

// 查询享惠名单列表
export function getEntApplyCompenylist(data) {
  return request({
    url: getBaseUrl() + '/workflow/ent/apply/compenylist',
    data,
    method: 'post'
  })
}

// 审批单补正提交接口
export function billProvesSubmit(data) {
  return request({
    url: getBaseUrl() + '/appBillProves/submit',
    data,
    method: 'post'
  })
}

// 审批单补正提交接口（零关税）
export function billProvesNewSubmit(data) {
  return request({
    url: getBaseUrl() + '/appBillProves/newSubmit',
    data,
    method: 'post'
  })
}

// 查看回执
export function isLianheVerify(params) {
  return request({
    url: getBaseUrl() + '/workflow/ent/apply/isLianheVerify',
    method: 'get',
    params
  })
}

// 撤回申请单
export function applyRecall(data) {
  return request({
    url: getBaseUrl() + '/workflow/ent/apply/recall',
    data,
    method: 'post'
  })
}

// 预提醒提交
export function updateBillWarn(data) {
  return request({
    url: getBaseUrl() + '/appBillWarn/update',
    data,
    method: 'post'
  })
}

// 预提醒列表
export function getWarnlist(data) {
  return request({
    url: getBaseUrl() + '/appBillWarn/warnlist',
    data,
    method: 'post'
  })
}

// 申报类型校验
export function submitCheck(data) {
  return request({
    url: getBaseUrl() + '/workflow/ent/apply/submitCheck',
    data,
    method: 'post'
  })
}

// 通知列表
export function getNotificationList(data) {
  return request({
    url: getBaseUrl() + '/workflow/ent/apply/compenylist',
    data,
    method: 'post'
  })
}

// 自产货物列表
export function getSelfProductGoodsList(data) {
  return request({
    url: getBaseUrl() + '/workflow/ent/apply/compenylist',
    data,
    method: 'post'
  })
}

// 自产货物去变更
export function selfProductGoodsToChange(params) {
  return request({
    url: getBaseUrl() + '/workflow/ent/apply/toChange',
    params,
    method: 'get'
  })
}

// 零关税去申报获取关联单据
export function getEntApplyToSubmit(data) {
  return request({
    url: getBaseUrl() + '/workflow/ent/apply/toSubmit',
    data,
    method: 'post'
  })
}

// 查询资质操作类型单据列表
export function getSelectAllApplyAppBillList(data) {
  return request({
    url: getBaseUrl() + '/workflow/ent/apply/selectAllApplyAppBillList',
    data,
    method: 'post'
  })
}
