<template>
  <page-drawer
    ref="pageDrawer"
    :visible="visible"
    :form-bl-id-head="detailId"
    :form-option="formDetailsOption"
    :form-data="formData"
    :form-option1="formDetailsOption1"
    :form-data1="formData1"
    :title="'详情'"
    :show-change-tip="true"
    @update:visible="updateVisible"
  ></page-drawer>
</template>

<script>
import { formOption as formDetailsOption } from '@/views/account/goods-import/zero-tariff-simplify/add-invt/account-invt/account-invt-details-options'
import AccountInvtFormData from '@/views/account/goods-import/zero-tariff-simplify/add-invt/account-invt/account-invt'
import { formOption as formDetailsOption1 } from '@/views/account/goods-import/zero-tariff-simplify/add-invt/account-invt-material/account-invt-material-details-options'
import AccountInvtMaterialFormData from '@/views/account/goods-import/zero-tariff-simplify/add-invt/account-invt-material/account-invt-material'
import pageDrawer from '@/components/account/pageDrawer.vue'

export default {
  components: {
    pageDrawer
  },

  props: {
    // 可见
    visible: {
      type: Boolean,
      default: false
    },
    // 详情id
    detailId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      formDetailsOption,
      formData: new AccountInvtFormData(this),
      formDetailsOption1,
      formData1: new AccountInvtMaterialFormData(this)
    }
  },
  computed: {
  },
  watch: {
    visible() {
      if (this.visible) {
        this.init()
      }
    }
  },
  created() {
  },
  methods: {
    updateVisible() {
      this.$emit('update:visible', false)
    },
    init() {
      if (this.formData) {
        this.formData.disabled.formDisabled = true
      }
      for (let i = 1; i <= 7; i++) {
        const formDataKey = `formData${i}`
        if (this[formDataKey] && this[formDataKey].url) {
          this[formDataKey].disabled.formDisabled = true
        }
      }
      this.$nextTick(() => {
        this.$refs.pageDrawer.modalActiveHandler()
      })
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
