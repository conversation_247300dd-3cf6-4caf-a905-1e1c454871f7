import request from '@/utils/request'

// 采购列表
export function getPurchaseList(data) {
  return request({
    url: 'accountZchwrdRecord/purchasePage',
    method: 'post',
    data
  })
}

// 操作
export function handlePurchase(data) {
  return request({
    url: 'accountZchwrdRecord/ope',
    method: 'post',
    data
  })
}
/**
 * 认定企业列表
 * @param {*} data
 * @returns
 */
export function listRdCompany(data) {
  return request({
    url: '/accountZchwrd/listRdCompany',
    method: 'post',
    data
  })
}

/**
 * 可采购自产货物分页列表
 * @param {*} data
 * @returns
 */
export function availablePurchasePage(data) {
  return request({
    url: '/accountZchwrd/availablePurchasePage',
    method: 'post',
    data
  })
}

/**
 * 采购
 * @param {*} data
 * @returns
 */
export function buy(data) {
  return request({
    url: '/accountZchwrd/buy',
    method: 'post',
    data
  })
}

// 自产货物目录列表分页查询
export function getZchwList(data) {
  return request({
    url: 'accountZchwrd/list',
    method: 'post',
    data
  })
}

// 自产货物交易明细
export function getSaleDetail(data) {
  return request({
    url: 'accountZchwrd/saleDetail',
    method: 'post',
    data
  })
}

// 销售
export function sale(data) {
  return request({
    url: 'accountZchwrd/sale',
    method: 'post',
    data
  })
}

// 销售台账分页查询
export function getSaleList(data) {
  return request({
    url: 'accountZchwrd/saleList',
    method: 'post',
    data
  })
}

// 销售详情
export function getSaleData(data) {
  return request({
    url: 'accountZchwrd/recordDetail',
    method: 'post',
    data
  })
}

// 自产货物采购变更
export function getPurchaseChange(data) {
  return request({
    url: 'accountZchwrdRecord/changesTemporary',
    method: 'post',
    data
  })
}

// 自产货物采购快捷提交
export function purchaseQuickSubmit(data) {
  return request({
    url: 'accountZchwrdRecord/ope',
    method: 'post',
    data
  })
}

// 自产货物采购作废
export function getPurchaseCancel(data) {
  return request({
    url: 'accountZchwrdRecord/buyerCancel',
    method: 'post',
    data
  })
}

// 查看回执
export function getReceipt(params) {
  return request({
    url: '/customReturnRecord/list',
    method: 'get',
    params
  })
}
