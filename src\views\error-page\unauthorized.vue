<template>
  <div>
    <div class="tips">您不是单一窗口企业用户或您的账号没有统一社会信用代码，无此操作权限！</div>
    <!-- <div class="btn-group">
      <el-button type="primary" @click="logout">退出登录</el-button>
    </div> -->
  </div>
</template>
<script>
import { removeSession } from '@/utils/auth'
import { logout } from '@/api/user'
export default {
  methods: {
    logout() {
      removeSession()
      logout()
        .then(res => {
          if (res.success) {
            window.location.href = res.message
          }
        })
        .catch(e => {
          console.log(e)
        })
    }
  }
}
</script>
<style lang="scss" scoped>
.tips {
  padding: 50px 0;
  text-align: center;
  font-size: 24px;
}
.btn-group {
  text-align: center;
}
</style>
