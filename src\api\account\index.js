import request from '@/utils/request'

// 获取列表
export function getListApi(url, params, method = 'get') {
  if (method === 'get') {
    return request({
      url: url,
      method,
      params
    })
  } else {
    return request({
      url: url,
      method,
      data: params
    })
  }
}

// 新增
export function addListApi(url, params) {
  return request({
    url: url,
    method: 'post',
    data: params
  })
}

// 编辑
export function updateListApi(url, params) {
  return request({
    url: url,
    method: 'put',
    data: params
  })
}

// 退单编辑
export function updateQuitListApi(url, params) {
  return request({
    url: url,
    method: 'post',
    data: params
  })
}

// 删除
export function deleteListApi(url, blId) {
  return request({
    url: url + '?blId=' + blId,
    method: 'delete'
  })
}
// 批量删除
export function deleteBathListApi(url, params) {
  return request({
    url: url + '/batchRemove',
    method: 'post',
    data: params
  })
}

// 区外单条申报
export function applySubmit(url, params) {
  return request({
    url: url + '/submit',
    method: 'post',
    data: params
  })
}

// 区内单条申报
export function applySubmitTwo(url, params) {
  const parts = url.split('/').filter((p) => p !== '')
  if (parts.length > 0 && parts[parts.length - 1] === 'insert') {
    parts.pop()
  }
  const urlNew = '/' + parts.join('/')
  return request({
    url: urlNew + '/submit',
    method: 'post',
    data: params
  })
}

// 复制
export function copy(url, params) {
  return request({
    url: url + '/copy',
    method: 'post',
    data: params
  })
}

// 变更
export function change(url, params) {
  return request({
    url: url + '/change',
    method: 'post',
    data: params
  })
}

// 操作回执
export function handleLog(url, params) {
  return request({
    url,
    method: 'get',
    params
  })
}

// 一键三联申报
export function oneToMoreSubmit(url, params) {
  return request({
    url: url + '/submit',
    method: 'post',
    data: params
  })
}

// 获取申报状态
export function getStatus(url, params) {
  return request({
    url,
    method: 'get',
    params
  })
}
// 获取一键三联列表
export function oneToMoreList(params) {
  return request({
    url: '/unionDec/list',
    method: 'get',
    params
  })
}

// 获取一键三联详情
export function oneToMoreDetail(params) {
  return request({
    url: '/unionDec',
    method: 'get',
    params
  })
}

// 物流授权分页列表
export function getLogisticsAuthList(data) {
  return request({
    url: '/logistics/pageLogisticsAuth',
    data,
    method: 'post'
  })
}

// 物流授权操作
export function logisticsDoAuth(data) {
  return request({
    url: '/logistics/doAuth',
    data,
    method: 'post'
  })
}

// 生成物流授权码
export function createLogisticsAuthCode(data) {
  return request({
    url: '/logistics/generateAuthCode',
    data,
    method: 'post'
  })
}

// 物流授权列表导出
export function logisticsExport(data) {
  return request({
    url: '/logistics/export',
    data,
    method: 'post',
    responseType: 'blob'
  })
}

// 获取常用车辆
export function getVehicleList(data) {
  return request({
    url: '/logistics/listCommonVehicleInfo',
    data,
    method: 'post'
  })
}

// 企业常用物流基础信息
export function getTransportCommonInfo(data) {
  return request({
    url: '/logistics/listTransportCommonInfo',
    data,
    method: 'post'
  })
}

// 获取物流运输信息
export function getTransportVehicleInfoList(data) {
  return request({
    url: '/logistics/getTransportVehicleInfo',
    data,
    method: 'post'
  })
}

// 获取常用承运商信息
export function getCommonCarrierInfo(data) {
  return request({
    url: '/logistics/listCommonCarrierInfo',
    data,
    method: 'post'
  })
}

// 享惠主体查询转入企业
export function getZcCompanyList(params) {
  return request({
    url: '/sysCompany/zcCompanyList',
    params,
    method: 'get'
  })
}

// 账册清单页面使用：账册编号下拉数据
export function getSingleList(params) {
  return request({
    url: '/prcsCommon/selectSingleList',
    params,
    method: 'get'
  })
}

// 批量申报提交单据
export function batchSubmitSave(data) {
  return request({
    url: '/sasPassportBsc/batchSubmitSave',
    data,
    method: 'post'
  })
}
