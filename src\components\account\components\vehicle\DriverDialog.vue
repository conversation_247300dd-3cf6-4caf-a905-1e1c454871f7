<template>
  <el-dialog
    :title="title"
    :visible.sync="visible"
    width="700px"
    modal-append-to-body
    append-to-body
    @close="handleClose"
  >

    <el-form class="form-wrap" :model="filterForm" :inline="true" size="small" label-positio="right">
      <el-row>
        <el-col :span="24">
          <el-form-item label="司机名称：">
            <el-input v-model="filterForm.driverName" placeholder="请输入关键字" @input="driverNameInput"></el-input>
          </el-form-item>
          <!-- <el-form-item>
            <el-button type="primary" size="small" :loading="listLoading" @click="onSearch">查询</el-button>
          </el-form-item> -->
        </el-col>
      </el-row>
    </el-form>

    <el-table
      ref="tableRef"
      border
      class="custom-table-account full-width"
      :data="listData"
      @row-click="handleRowClick"
    >
      <el-table-column align="center" width="50px">
        <template slot-scope="scope">
          <span>
            <el-checkbox v-model="scope.row.checked" @change="checkboxChange(scope.row)"></el-checkbox>
          </span>
        </template>
      </el-table-column>
      <el-table-column label="司机名称" align="center" show-overflow-tooltip min-width="120px">
        <template slot-scope="scope">
          <span>{{ scope.row.driverName }}</span>
        </template>
      </el-table-column>
      <el-table-column label="司机手机号" align="center" show-overflow-tooltip min-width="160px">
        <template slot-scope="scope">
          <span>{{ scope.row.driverPhone }}</span>
        </template>
      </el-table-column>
    </el-table>

    <!-- <div class="pagination-wrap">
      <el-pagination
        :current-page="pageable.pageNo"
        :page-sizes="[6, 20, 50, 100]"
        :page-size="pageable.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pageable.total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      >
      </el-pagination>
    </div> -->

    <div class="footer-wrap">
      <el-button size="small" @click="handleClose">关闭</el-button>
      <el-button type="primary" size="small" @click="handleConfirm">确定</el-button>
    </div>

  </el-dialog>
</template>

<script>
import dayjs from 'dayjs'
import { getTransportCommonInfo } from '@/api/account/index'
import dict from '@/utils/dict'
import { debounce } from '@/utils/index'

export default {
  components: {
  },
  props: {
    title: {
      type: String,
      default: '司机信息'
    },
    // 可见
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      dayjs,
      dict,
      listLoading: false,
      allData: [],
      listData: [],
      filterForm: {
        driverName: ''
      },
      pageable: {
        total: 0,
        pageNo: 1,
        pageSize: 6
      }
    }
  },
  computed: {
  },
  created() {
    this.requestList()
  },
  methods: {
    driverNameInput: debounce(
      function() {
        this.listData = this.allData.filter(i => i.driverName.indexOf(this.filterForm.driverName) !== -1)
      },
      200
    ),
    handleClose() {
      this.$emit('close')
    },
    handleConfirm() {
      const arr = this.listData.filter(i => i.checked)
      if (!arr.length) {
        this.$message.error('请选择')
        return
      }
      this.$emit('confirm', arr)
    },
    handleSizeChange(val) {
      this.pageable.pageNo = 1
      this.pageable.pageSize = val
      this.requestList()
    },
    handleCurrentChange(val) {
      this.pageable.pageNo = val
      this.requestList()
    },
    onSearch() {
      this.pageable.pageNo = 1
      this.requestList()
    },
    handleRowClick(row) {
      this.listData.forEach((item) => {
        item.checked = false
      })
      row.checked = true
    },
    checkboxChange(row) {
      this.listData.forEach((item) => {
        item.checked = false
      })
      row.checked = true
    },

    // 请求
    async requestList() {
      try {
        this.listLoading = true
        const params = {
          pageNo: this.pageable.pageNo,
          pageSize: this.pageable.pageSize
          // ...this.filterForm
        }
        const res = await getTransportCommonInfo(params)
        if (res.isSuccess) {
          const list = res?.data?.driverList || []
          const total = 0
          list.forEach((item) => {
            if (item.checked) {
              item.checked = true
            } else {
              item.checked = false
            }
          })
          this.allData = list
          this.listData = list
          this.pageable.total = total
        }
        this.listLoading = false
      } catch (error) {
        this.listLoading = false
        console.log(error)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-form .el-form-item{
  margin: 0;
}
.pagination-wrap {
  padding: 16px 0 0 0;
}
.footer-wrap{
  padding: 20px 0 0 0;
  text-align: right;
}
</style>
